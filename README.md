# 🤖 AI-Powered Hotel Portfolio Query Tool

An advanced, AI-powered natural language query tool for hotel portfolio management. This tool leverages OpenAI's GPT-4 to provide intelligent, context-aware responses to complex queries about your hotel portfolio.

## ✨ Enhanced Features

### 🧠 **AI-Powered Query Processing**
- **OpenAI GPT-4 Integration**: Advanced natural language understanding
- **Intelligent Intent Recognition**: Understands complex, multi-criteria queries
- **Context-Aware Responses**: Provides relevant, conversational answers
- **Smart Query Analysis**: Extracts entities, filters, and preferences automatically

### 🎯 **Advanced Natural Language Processing**
- Process complex queries like "Find luxury hotels with swimming pool and spa in Mumbai under 5000"
- Understand comparative queries: "Compare amenities between hotels in Goa and Pondicherry"
- Handle temporal queries: "I need a hotel for next week with twin beds and breakfast"
- Support for business queries: "Best business hotels with conference rooms and wifi"

### 🗄️ **Database Integration**
- **PostgreSQL Integration**: Fast, scalable data storage
- **Intelligent Caching**: API response caching for better performance
- **Fuzzy Search**: Advanced text similarity matching
- **Geographic Queries**: Location-based search with radius filtering
- **Analytics Dashboard**: Portfolio insights and statistics

### 🏨 **Enhanced Hotel Information**
- Complete hotel portfolio data from database
- Real-time amenity information via Treebo Catalog API
- Hotel and room-level amenity details
- Location hierarchy (State → City → Locality → Micro Market)
- Relevance scoring for search results

### 🔍 **Query Types Supported**
1. **Specific Hotel Queries**: "Does Treebo Paradise have spa facilities?"
2. **City + Amenity Search**: "Hotels with swimming pool in Pondicherry"
3. **General Amenity Search**: "Hotels with banquet facilities"
4. **Location Search**: "All hotels in Bangalore"
5. **Room Type Queries**: "Twin bed availability in Indore"

### 🌐 **Web Interface**
- Clean, responsive web interface
- Real-time query processing
- Interactive results display
- Example queries for guidance

## 🚀 Installation & Setup

### Prerequisites
- Python 3.8+
- PostgreSQL 12+ (for database features)
- OpenAI API Key (for AI features)
- pip (Python package manager)

### Quick Setup

1. **Clone or download the project**
   ```bash
   cd /path/to/your/project
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` file with your configuration:
   ```env
   # OpenAI Configuration (Required for AI features)
   OPENAI_API_KEY=your-openai-api-key-here

   # Database Configuration
   DB_HOST=localhost
   DB_NAME=local_hackathon
   DB_USER=ashokkumar
   DB_PASSWORD=your-password
   ```

5. **Set up PostgreSQL database**
   ```bash
   # Create database (if not exists)
   createdb local_hackathon

   # Run database setup script
   python setup_database.py
   ```

6. **Ensure hotel data is available**
   - Make sure `hotel_metadata.csv` is in the project root
   - The setup script will import this data into PostgreSQL

7. **Run the application**
   ```bash
   python app.py
   ```

8. **Access the application**
   - Open your browser and go to `http://localhost:5000`
   - Try the AI-powered example queries!

## Usage

### Web Interface

1. **Open the application** in your browser
2. **Type your query** in natural language
3. **Click Search** or press Enter
4. **View results** with hotel information and amenities

### Example Queries

```
Which hotel has banquet in Nagpur?
Hotels with swimming pool in Pondicherry
Does Treebo Paradise have spa facilities?
Twin bed availability in Indore
All hotels in Bangalore
Hotels with parking in Mumbai
Treebo Amber International amenities
Hotels with breakfast in Delhi
Swimming pool hotels in Goa
Banquet facilities near airport
```

### API Endpoints

#### POST `/api/query`
Process natural language queries
```json
{
  "query": "Hotels with swimming pool in Bangalore"
}
```

#### GET `/api/hotels`
Get all hotels with basic information

#### GET `/api/hotels/<property_id>`
Get detailed information for a specific hotel

#### GET `/api/cities`
Get all available cities

#### GET `/api/amenities`
Get all available amenity types

#### POST `/api/search`
Advanced search with filters
```json
{
  "city": "Bangalore",
  "amenities": ["swimming_pool", "spa"],
  "property_name": "Treebo"
}
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SECRET_KEY` | Flask secret key | `your-secret-key-here` |
| `DEBUG` | Enable debug mode | `True` |
| `TREEBO_API_BASE_URL` | Base URL for hotel API | `https://catalog.treebo.com/...` |
| `API_TIMEOUT` | API request timeout (seconds) | `10` |
| `HOTEL_METADATA_FILE` | Path to hotel CSV file | `hotel_metadata.csv` |
| `SIMILARITY_THRESHOLD` | Text similarity threshold | `0.6` |
| `FUZZY_MATCH_THRESHOLD` | Fuzzy matching threshold | `60` |
| `MAX_RESULTS` | Maximum results to return | `50` |

### Hotel Data Format

The `hotel_metadata.csv` file should contain the following columns:
- `property_id`: Unique hotel identifier
- `name`: Hotel name
- `status`: Hotel status (LIVE, etc.)
- `city_name`: City name
- `locality_name`: Locality/area name
- `micro_market_name`: Micro market name
- `latitude`: Latitude coordinate
- `longitude`: Longitude coordinate
- `postal_address`: Full address
- `maps_link`: Google Maps link

## Architecture

### Components

1. **Flask Application** (`app.py`)
   - Main web server and API endpoints
   - Request handling and response formatting

2. **Query Processor** (`services/query_processor.py`)
   - Natural language query analysis
   - Intent detection and entity extraction
   - Query routing and response formatting

3. **Hotel Service** (`services/hotel_service.py`)
   - Hotel data management
   - API integration for detailed information
   - Search and filtering capabilities

4. **NLP Utils** (`utils/nlp_utils.py`)
   - Text processing and normalization
   - Similarity matching and fuzzy search
   - Entity extraction utilities

5. **Web Interface** (`templates/index.html`)
   - Responsive web UI
   - Real-time query processing
   - Results visualization

### Data Flow

1. User enters natural language query
2. Query processor analyzes intent and extracts entities
3. Hotel service searches data based on extracted criteria
4. API calls made for detailed amenity information
5. Results formatted and returned to user

## Supported Amenities

- Swimming Pool
- Spa & Wellness
- Banquet Hall / Conference Room
- Parking (including Valet)
- Breakfast
- Elevator / Lift
- Laundry Service
- WiFi / Internet
- Gym / Fitness Center
- Restaurant / Dining
- Room Service
- Air Conditioning
- Television
- Twin Bed / Double Bed
- Balcony / Terrace
- Scenic Views

## Troubleshooting

### Common Issues

1. **"Hotel data not found"**
   - Ensure `hotel_metadata.csv` exists in project root
   - Check file permissions and format

2. **API connection errors**
   - Verify internet connection
   - Check API endpoint URL in configuration
   - Review API timeout settings

3. **No results for queries**
   - Try simpler queries
   - Check spelling of city names and hotel names
   - Use example queries as reference

4. **Slow response times**
   - Increase API timeout in configuration
   - Check network connectivity
   - Consider enabling caching

### Logs

Application logs are written to `hotel_query_tool.log` by default. Check this file for detailed error information.

## Development

### Adding New Amenities

1. Update `amenity_keywords` in `services/query_processor.py`
2. Add mapping in `services/hotel_service.py`
3. Update amenity extraction logic if needed

### Extending Query Types

1. Add new patterns to `query_patterns` in `services/query_processor.py`
2. Implement handler method for new query type
3. Update response formatting

### API Integration

The tool integrates with the Treebo catalog API:
```
https://catalog.treebo.com/cataloging-service/api/v2/properties/?property_id=<property_id>
```

Modify `services/hotel_service.py` to adapt to different API structures.

## License

This project is for internal use within the organization.

## Support

For technical support or feature requests, please contact the development team.
