import dspy

# Configure DSPy with OpenAI
lm = dspy.LM('openai/gpt-4o-mini',
             api_key='********************************************************************************************************************************************************************')

print("=== DSPy RM Flow Demonstration ===\n")

# Step 1: Create RM
print("1. Creating Retrieval Model...")
rm = dspy.ColBERTv2(url='http://20.102.90.50:2017/wiki17_abstracts')
print(f"   RM created: {type(rm)}")
print(f"   RM object: {rm}")

# Step 2: Configure DSPy globally
print("\n2. Configuring DSPy globally...")
dspy.configure(lm=lm, rm=rm)
print("   ✅ DSPy configured with LM and RM")

# Step 3: Check what's in the global config
print("\n3. Checking DSPy global configuration...")
try:
    current_lm = dspy.settings.lm
    current_rm = dspy.settings.rm
    print(f"   Global LM: {type(current_lm)}")
    print(f"   Global RM: {type(current_rm)}")
    print(f"   RM is same object: {current_rm is rm}")
except Exception as e:
    print(f"   Could not access settings: {e}")

# Step 4: Create Retrieve module (gets RM automatically)
print("\n4. Creating dspy.Retrieve module...")
retriever = dspy.Retrieve(k=2)
print(f"   Retriever created: {type(retriever)}")
print(f"   Retriever uses global RM automatically")

# Step 5: Use the retriever
print("\n5. Using the retriever...")
try:
    result = retriever("famous places in India")
    print(f"   Query: 'famous places in India'")
    print(f"   Result type: {type(result)}")
    if hasattr(result, 'passages'):
        print(f"   Number of passages: {len(result.passages)}")
        print(f"   First passage preview: {result.passages[0][:100]}...")
except Exception as e:
    print(f"   Retrieval failed: {e}")

# Step 6: Show that multiple Retrieve instances use the same RM
print("\n6. Multiple Retrieve instances...")
retriever2 = dspy.Retrieve(k=1)
retriever3 = dspy.Retrieve(k=5)
print(f"   All retrievers use the same global RM")
print(f"   No need to pass RM manually to each instance")

print("\n=== Summary ===")
print("✅ RM is passed through DSPy's global configuration")
print("✅ dspy.configure(rm=rm) makes RM available globally")
print("✅ dspy.Retrieve() automatically uses the configured RM")
print("✅ No manual RM passing required in your code")
