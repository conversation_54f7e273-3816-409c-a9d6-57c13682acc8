import dspy

# Configure DSPy
dspy.configure(lm=dspy.LM("openai/gpt-4o", api_key="YOUR_API_KEY"))

# Define the Task
sentiment_task = dspy.Task(
    name="Sentiment Analysis",
    signature="text -> sentiment",
    examples=[
        {"text": "I love this movie!", "sentiment": "positive"},
        {"text": "This is the worst day ever.", "sentiment": "negative"},
        {"text": "It's okay, not great but not bad either.", "sentiment": "neutral"}
    ]
).compile()

# Test it
resp = sentiment_task(text="I’m so happy with my new phone!")
print("Sentiment:", resp.sentiment)

