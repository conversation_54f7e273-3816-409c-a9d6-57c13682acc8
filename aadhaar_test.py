import jwt
import datetime
import gzip
import qrcode
from PIL import Image
import traceback

# Load the RSA private key from PEM file
with open('aadhaar-integration-private.pem', 'r') as key_file:
    SECRET_KEY = key_file.read()

ALGORITHM = "RS256"

# Set timestamps
iat = int(datetime.datetime.utcnow().timestamp())
exp = iat + 300  # Valid for 5 minutes

# JWT Payload
payload = {
    "txn": "94f63917-708a-4e58-12",
    "i": "credential",
    "lang": "23",
    "sc": "00001100000100000000000000000000001010101",
    "pop": 1,
    "ch": "app",
    "m": "1",
    "ac": "000",
    "sa": "00b1",
    "cb": "http://localhost:3000/dev/local/api/v1/aadhar/callback",
    "aud": "https://myaadhaarstage.uidai.gov.in",
    "iss": "https://myaadhaarstage.uidai.gov.in/v1/esignet",
    "exp": exp,
    "iat": iat,
    "ht": "Name of the person",
    "aid": "",
    "asig": "",
    "jti": "8a9d7c2b-b932-4131-9e36-43f22a019be5"
}

# Create JWT
jwt_token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
print("🔐 JWT Token:")
print(jwt_token)


# Encoding and QR steps
def jwt_to_iso88591_bytes(jwt_string):
    return jwt_string.encode('ISO-8859-1')


def append_delimiter(data):
    return data + bytes([255])


def compress_data(data):
    return gzip.compress(data)


def byte_array_to_bigint(byte_array):
    return int.from_bytes(byte_array, byteorder='big')


def bigint_to_base10_string(bigint):
    return str(bigint)


def generate_qr_code_value_from_jwt(jwt_string):
    byte_array = jwt_to_iso88591_bytes(jwt_string)
    byte_array_with_delimiter = append_delimiter(byte_array)
    compressed_data = compress_data(byte_array_with_delimiter)
    big_int = byte_array_to_bigint(compressed_data)
    return bigint_to_base10_string(big_int)


# Generate QR base10 string
qr_code_value = generate_qr_code_value_from_jwt(jwt_token)
print("🔢 QR Code Base10 Value:")
print(qr_code_value)


# Generate QR code (no logo)
def generate_qr_code(data, file_name='qrcodessss.png'):
    print("🧾 Generating QR code...")
    qr = qrcode.QRCode(
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img_qr = qr.make_image(fill_color="black", back_color="white").convert("RGBA")

    try:
        img_qr.save(file_name)
        print(f"✅ QR code saved as {file_name}")
    except Exception as e:
        print("❌ Error saving QR code image:")
        traceback.print_exc()


# Save the QR code image
generate_qr_code(f"https://maadhaar.com/getIntent?value={qr_code_value}")
print(generate_qr_code)

print(len(qr_code_value))
