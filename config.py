import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """
    Configuration settings for the Hotel Portfolio Query Tool
    """

    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'

    # API settings
    TREEBO_API_BASE_URL = os.environ.get('TREEBO_API_BASE_URL') or 'https://catalog.treebo.com/cataloging-service/api/v2/properties/'
    API_TIMEOUT = int(os.environ.get('API_TIMEOUT', 10))

    # OpenAI settings
    OPENAI_API_KEY = "********************************************************************************************************************************************************************"
    OPENAI_MODEL = os.environ.get('OPENAI_MODEL', 'gpt-4-turbo-preview')
    OPENAI_MAX_TOKENS = int(os.environ.get('OPENAI_MAX_TOKENS', 1000))

    # Database settings
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'postgresql://ashokkumar@localhost/local_hackathon'
    DB_HOST = os.environ.get('DB_HOST', 'localhost')
    DB_PORT = int(os.environ.get('DB_PORT', 5432))
    DB_NAME = os.environ.get('DB_NAME', 'local_hackathon')
    DB_USER = os.environ.get('DB_USER', 'ashokkumar')
    DB_PASSWORD = os.environ.get('DB_PASSWORD', '')

    # Data settings
    HOTEL_METADATA_FILE = os.environ.get('HOTEL_METADATA_FILE') or 'hotel_metadata.csv'

    # Cache settings
    ENABLE_CACHE = os.environ.get('ENABLE_CACHE', 'True').lower() == 'true'
    CACHE_TIMEOUT = int(os.environ.get('CACHE_TIMEOUT', 3600))  # 1 hour

    # Logging settings
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'hotel_query_tool.log')

    # NLP settings - Lowered thresholds for better recall
    SIMILARITY_THRESHOLD = float(os.environ.get('SIMILARITY_THRESHOLD', 0.4))
    FUZZY_MATCH_THRESHOLD = int(os.environ.get('FUZZY_MATCH_THRESHOLD', 50))

    # Search confidence settings
    HIGH_CONFIDENCE_THRESHOLD = float(os.environ.get('HIGH_CONFIDENCE_THRESHOLD', 0.6))
    MEDIUM_CONFIDENCE_THRESHOLD = float(os.environ.get('MEDIUM_CONFIDENCE_THRESHOLD', 0.4))
    LOW_CONFIDENCE_THRESHOLD = float(os.environ.get('LOW_CONFIDENCE_THRESHOLD', 0.2))

    # Query processing settings
    MAX_RESULTS = int(os.environ.get('MAX_RESULTS', 50))
    DEFAULT_RESULTS_LIMIT = int(os.environ.get('DEFAULT_RESULTS_LIMIT', 20))

    # Search quality settings
    ENABLE_FUZZY_SEARCH = os.environ.get('ENABLE_FUZZY_SEARCH', 'True').lower() == 'true'
    ENABLE_PARTIAL_MATCHES = os.environ.get('ENABLE_PARTIAL_MATCHES', 'True').lower() == 'true'
    MINIMUM_MATCH_LENGTH = int(os.environ.get('MINIMUM_MATCH_LENGTH', 3))

    # API reliability settings
    API_RETRY_COUNT = int(os.environ.get('API_RETRY_COUNT', 3))
    API_RETRY_DELAY = float(os.environ.get('API_RETRY_DELAY', 1.0))

    # Rate limiting (if needed)
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'False').lower() == 'true'
    RATE_LIMIT_PER_MINUTE = int(os.environ.get('RATE_LIMIT_PER_MINUTE', 60))

    @staticmethod
    def init_app(app):
        """Initialize the Flask app with configuration"""
        pass

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

    # More lenient thresholds for development
    SIMILARITY_THRESHOLD = 0.3
    FUZZY_MATCH_THRESHOLD = 40
    HIGH_CONFIDENCE_THRESHOLD = 0.5
    MEDIUM_CONFIDENCE_THRESHOLD = 0.3
    LOW_CONFIDENCE_THRESHOLD = 0.1

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    RATE_LIMIT_ENABLED = True

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    HOTEL_METADATA_FILE = 'test_hotel_metadata.csv'

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
