#!/usr/bin/env python3
"""
Enhanced test script to verify the AI-Powered Hotel Portfolio Query Tool setup
"""

import os
import sys
import pandas as pd
from services.hotel_service import HotelService
from services.database_service import DatabaseService
from services.ai_query_processor import AIQueryProcessor
from utils.nlp_utils import NLPUtils
from config import Config

def test_csv_data():
    """Test if hotel metadata CSV is properly loaded"""
    print("🔍 Testing CSV data loading...")

    if not os.path.exists('hotel_metadata.csv'):
        print("❌ hotel_metadata.csv not found!")
        return False

    try:
        df = pd.read_csv('hotel_metadata.csv')
        print(f"✅ CSV loaded successfully with {len(df)} rows")

        # Check required columns
        required_columns = ['property_id', 'name', 'city_name', 'status']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"❌ Missing required columns: {missing_columns}")
            return False

        # Check for LIVE properties
        live_properties = df[df['status'] == 'LIVE']
        print(f"✅ Found {len(live_properties)} LIVE properties")

        # Show sample cities
        cities = df['city_name'].dropna().unique()
        print(f"✅ Available cities: {len(cities)} (sample: {list(cities[:5])})")

        return True

    except Exception as e:
        print(f"❌ Error loading CSV: {str(e)}")
        return False

def test_hotel_service():
    """Test hotel service functionality"""
    print("\n🏨 Testing Hotel Service...")

    try:
        hotel_service = HotelService()
        hotel_service.load_hotel_data()

        # Test getting all hotels
        hotels = hotel_service.get_all_hotels()
        print(f"✅ Retrieved {len(hotels)} hotels")

        # Test getting cities
        cities = hotel_service.get_all_cities()
        print(f"✅ Retrieved {len(cities)} cities")

        # Test search functionality
        if cities:
            sample_city = cities[0]
            city_hotels = hotel_service.search_hotels(city=sample_city)
            print(f"✅ Found {len(city_hotels)} hotels in {sample_city}")

        return True

    except Exception as e:
        print(f"❌ Hotel service error: {str(e)}")
        return False

def test_nlp_utils():
    """Test NLP utilities"""
    print("\n🧠 Testing NLP Utils...")

    try:
        nlp = NLPUtils()

        # Test text cleaning
        test_text = "  Hotels with Swimming Pool in Mumbai!  "
        cleaned = nlp.clean_text(test_text)
        print(f"✅ Text cleaning: '{test_text}' -> '{cleaned}'")

        # Test keyword extraction
        keywords = nlp.extract_keywords("Hotels with swimming pool in Mumbai")
        print(f"✅ Keywords extracted: {keywords}")

        # Test entity extraction
        entities = nlp.extract_entities("Hotels with swimming pool in Mumbai")
        print(f"✅ Entities extracted: {entities}")

        # Test amenity normalization
        normalized = nlp.normalize_amenity_name("swimming pool")
        print(f"✅ Amenity normalization: 'swimming pool' -> '{normalized}'")

        return True

    except Exception as e:
        print(f"❌ NLP utils error: {str(e)}")
        return False

def test_query_processor():
    """Test query processor"""
    print("\n🔍 Testing Query Processor...")

    try:
        hotel_service = HotelService()
        hotel_service.load_hotel_data()

        query_processor = QueryProcessor(hotel_service)

        # Test sample queries
        test_queries = [
            "Hotels in Mumbai",
            "Hotels with swimming pool in Bangalore",
            "Does Treebo Paradise have spa?",
            "All hotels in Delhi"
        ]

        for query in test_queries:
            print(f"\n  Testing query: '{query}'")
            try:
                result = query_processor.process_query(query)
                print(f"  ✅ Query processed successfully, type: {result.get('type', 'unknown')}")
                if 'count' in result:
                    print(f"  📊 Results count: {result['count']}")
            except Exception as e:
                print(f"  ❌ Query failed: {str(e)}")

        return True

    except Exception as e:
        print(f"❌ Query processor error: {str(e)}")
        return False

def test_api_connection():
    """Test API connection"""
    print("\n🌐 Testing API Connection...")

    try:
        hotel_service = HotelService()
        hotel_service.load_hotel_data()

        # Get a sample property ID
        hotels = hotel_service.get_all_hotels()
        if not hotels:
            print("❌ No hotels available for API testing")
            return False

        sample_property_id = hotels[0]['property_id']
        print(f"  Testing API with property ID: {sample_property_id}")

        # Test API call
        details = hotel_service.get_hotel_details(sample_property_id)

        if details:
            print("✅ API connection successful")
            if 'error' in details:
                print(f"⚠️  API returned with error: {details['error']}")
            else:
                print("✅ API returned valid data")
            return True
        else:
            print("❌ API returned no data")
            return False

    except Exception as e:
        print(f"❌ API connection error: {str(e)}")
        return False

def test_database_connection():
    """Test database connection and functionality"""
    print("\n🗄️ Testing Database Connection...")

    try:
        db_service = DatabaseService()

        if not db_service.connection:
            print("❌ Database connection failed")
            return False

        # Test basic queries
        hotel_count = db_service.get_hotel_count()
        print(f"✅ Database connected, found {hotel_count} hotels")

        cities = db_service.get_all_cities()
        print(f"✅ Retrieved {len(cities)} cities")

        if cities:
            sample_city = cities[0]
            city_hotels = db_service.get_hotels_by_city(sample_city, limit=5)
            print(f"✅ Found {len(city_hotels)} hotels in {sample_city}")

        return True

    except Exception as e:
        print(f"❌ Database test error: {str(e)}")
        return False

def test_ai_features():
    """Test AI-powered features"""
    print("\n🤖 Testing AI Features...")

    try:
        if not Config.OPENAI_API_KEY:
            print("⚠️  OpenAI API key not configured")
            return False

        # Test AI query processor initialization
        db_service = DatabaseService()
        hotel_service = HotelService(db_service)
        ai_processor = AIQueryProcessor(hotel_service, db_service)

        print("✅ AI Query Processor initialized")

        # Test a simple AI query (without making actual API call to save costs)
        test_query = "Hotels with swimming pool in Mumbai"
        print(f"  Testing query analysis for: '{test_query}'")

        # Test fallback analysis
        analysis = ai_processor._fallback_analysis(test_query)
        print(f"✅ Query analysis completed: {analysis.get('intent', 'unknown')} intent")

        return True

    except Exception as e:
        print(f"❌ AI features test error: {str(e)}")
        return False

def test_enhanced_search():
    """Test enhanced search capabilities"""
    print("\n🔍 Testing Enhanced Search...")

    try:
        db_service = DatabaseService()

        # Test multi-criteria search
        search_params = {
            'cities': ['Mumbai', 'Bangalore'],
            'limit': 5
        }

        results = db_service.search_hotels(**search_params)
        print(f"✅ Multi-city search returned {len(results)} results")

        # Test fuzzy name search
        if results:
            sample_hotel = results[0]['name']
            # Search with partial name
            partial_name = sample_hotel[:5]
            fuzzy_results = db_service.search_hotels_by_name(partial_name, limit=3)
            print(f"✅ Fuzzy search for '{partial_name}' returned {len(fuzzy_results)} results")

        return True

    except Exception as e:
        print(f"❌ Enhanced search test error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 AI-Powered Hotel Portfolio Query Tool - Enhanced Setup Test\n")

    tests = [
        ("CSV Data Loading", test_csv_data),
        ("Database Connection", test_database_connection),
        ("Hotel Service", test_hotel_service),
        ("NLP Utils", test_nlp_utils),
        ("Enhanced Search", test_enhanced_search),
        ("AI Features", test_ai_features),
        ("API Connection", test_api_connection)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)

        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append((test_name, False))

    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)

    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1

    print(f"\nOverall: {passed}/{len(results)} tests passed")

    if passed == len(results):
        print("\n🎉 All tests passed! Your setup is ready.")
        print("\nYou can now run the application with:")
        print("  python app.py")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("- Ensure hotel_metadata.csv is in the project root")
        print("- Check internet connection for API tests")
        print("- Verify all dependencies are installed: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
