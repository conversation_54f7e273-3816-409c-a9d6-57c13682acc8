import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from fuzzywuzzy import fuzz

logger = logging.getLogger(__name__)

class NLPUtils:
    """
    Natural Language Processing utilities for query analysis
    """
    
    def __init__(self):
        # Common stop words to filter out
        self.stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'have', 'had', 'which', 'where',
            'what', 'when', 'who', 'how', 'can', 'could', 'should', 'would',
            'do', 'does', 'did', 'any', 'all', 'some', 'there', 'here'
        }
        
        # Question words that indicate query intent
        self.question_words = {
            'which', 'what', 'where', 'when', 'who', 'how', 'does', 'do',
            'is', 'are', 'can', 'could', 'would', 'should', 'find', 'show',
            'list', 'get', 'tell', 'give'
        }
        
        # Location indicators
        self.location_indicators = {
            'in', 'at', 'near', 'around', 'close to', 'nearby', 'within'
        }
        
        # Amenity indicators
        self.amenity_indicators = {
            'with', 'having', 'has', 'have', 'includes', 'featuring',
            'offers', 'provides', 'available', 'equipped with'
        }
        
        # Negation words
        self.negation_words = {
            'not', 'no', 'without', 'lacking', 'missing', 'absent',
            'dont', "don't", 'doesnt', "doesn't", 'isnt', "isn't"
        }
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text
        """
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep spaces and hyphens
        text = re.sub(r'[^\w\s\-]', ' ', text)
        
        # Remove extra spaces again
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def extract_keywords(self, text: str, remove_stop_words: bool = True) -> List[str]:
        """
        Extract keywords from text
        """
        text = self.clean_text(text)
        words = text.split()
        
        if remove_stop_words:
            words = [word for word in words if word not in self.stop_words]
        
        # Remove very short words (less than 2 characters)
        words = [word for word in words if len(word) >= 2]
        
        return words
    
    def detect_query_intent(self, query: str) -> Dict[str, Any]:
        """
        Detect the intent of the query
        """
        query_lower = query.lower()
        
        intent = {
            'type': 'unknown',
            'is_question': False,
            'has_negation': False,
            'confidence': 0.0
        }
        
        # Check if it's a question
        if any(word in query_lower for word in self.question_words):
            intent['is_question'] = True
        
        # Check for negation
        if any(word in query_lower for word in self.negation_words):
            intent['has_negation'] = True
        
        # Determine query type based on patterns
        if re.search(r'(which|what)\s+hotel', query_lower):
            intent['type'] = 'hotel_search'
            intent['confidence'] = 0.8
        elif re.search(r'does\s+.+\s+have', query_lower):
            intent['type'] = 'amenity_check'
            intent['confidence'] = 0.9
        elif re.search(r'hotels?\s+with\s+.+\s+in\s+', query_lower):
            intent['type'] = 'location_amenity_search'
            intent['confidence'] = 0.9
        elif re.search(r'hotels?\s+in\s+', query_lower):
            intent['type'] = 'location_search'
            intent['confidence'] = 0.8
        elif re.search(r'amenities\s+(at|in|of)', query_lower):
            intent['type'] = 'amenity_list'
            intent['confidence'] = 0.7
        
        return intent
    
    def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        Extract entities like locations, amenities, hotel names
        """
        text_lower = text.lower()
        entities = {
            'locations': [],
            'amenities': [],
            'hotel_names': [],
            'room_types': []
        }
        
        # Extract potential locations (words after location indicators)
        for indicator in self.location_indicators:
            pattern = rf'{indicator}\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)'
            matches = re.findall(pattern, text_lower)
            for match in matches:
                location = match.strip()
                if len(location) > 1 and location not in self.stop_words:
                    entities['locations'].append(location)
        
        # Extract potential amenities (words after amenity indicators)
        for indicator in self.amenity_indicators:
            pattern = rf'{indicator}\s+([a-zA-Z\s]+?)(?:\s+in\s+|\s+at\s+|$|,|\.|!|\?)'
            matches = re.findall(pattern, text_lower)
            for match in matches:
                amenity = match.strip()
                if len(amenity) > 1:
                    entities['amenities'].append(amenity)
        
        # Extract potential hotel names (capitalized words or words with "treebo", "hotel")
        hotel_patterns = [
            r'(treebo\s+[a-zA-Z\s]+)',
            r'(hotel\s+[a-zA-Z\s]+)',
            r'([A-Z][a-zA-Z]+\s+[A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*)'
        ]
        
        for pattern in hotel_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                hotel_name = match.strip()
                if len(hotel_name) > 3:
                    entities['hotel_names'].append(hotel_name)
        
        # Extract room types
        room_patterns = [
            r'(twin\s+bed[s]?)',
            r'(double\s+bed[s]?)',
            r'(single\s+bed[s]?)',
            r'(king\s+bed[s]?)',
            r'(queen\s+bed[s]?)',
            r'(deluxe\s+room[s]?)',
            r'(standard\s+room[s]?)',
            r'(suite[s]?)'
        ]
        
        for pattern in room_patterns:
            matches = re.findall(pattern, text_lower)
            entities['room_types'].extend(matches)
        
        # Remove duplicates and clean up
        for key in entities:
            entities[key] = list(set(entities[key]))
            entities[key] = [item.strip() for item in entities[key] if item.strip()]
        
        return entities
    
    def similarity_score(self, text1: str, text2: str) -> float:
        """
        Calculate similarity score between two texts
        """
        return fuzz.ratio(text1.lower(), text2.lower()) / 100.0
    
    def find_best_match(self, query: str, candidates: List[str], 
                       threshold: float = 0.6) -> Optional[Tuple[str, float]]:
        """
        Find the best matching candidate for a query
        """
        best_match = None
        best_score = 0.0
        
        query_lower = query.lower()
        
        for candidate in candidates:
            candidate_lower = candidate.lower()
            
            # Try different similarity measures
            ratio_score = fuzz.ratio(query_lower, candidate_lower) / 100.0
            partial_score = fuzz.partial_ratio(query_lower, candidate_lower) / 100.0
            token_score = fuzz.token_sort_ratio(query_lower, candidate_lower) / 100.0
            
            # Use the highest score
            score = max(ratio_score, partial_score, token_score)
            
            if score > best_score and score >= threshold:
                best_score = score
                best_match = candidate
        
        return (best_match, best_score) if best_match else None
    
    def extract_numbers(self, text: str) -> List[int]:
        """
        Extract numbers from text
        """
        numbers = re.findall(r'\b\d+\b', text)
        return [int(num) for num in numbers]
    
    def has_positive_sentiment(self, text: str) -> bool:
        """
        Simple positive sentiment detection
        """
        positive_words = {
            'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
            'best', 'perfect', 'love', 'like', 'recommend', 'beautiful',
            'nice', 'awesome', 'outstanding', 'superb'
        }
        
        negative_words = {
            'bad', 'terrible', 'awful', 'horrible', 'worst', 'hate',
            'dislike', 'poor', 'disappointing', 'unacceptable'
        }
        
        text_lower = text.lower()
        words = text_lower.split()
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        return positive_count > negative_count
    
    def extract_price_range(self, text: str) -> Optional[Tuple[float, float]]:
        """
        Extract price range from text
        """
        # Look for patterns like "under 5000", "between 2000 and 5000", "less than 3000"
        patterns = [
            r'under\s+(\d+)',
            r'less\s+than\s+(\d+)',
            r'below\s+(\d+)',
            r'between\s+(\d+)\s+and\s+(\d+)',
            r'from\s+(\d+)\s+to\s+(\d+)',
            r'(\d+)\s*-\s*(\d+)',
            r'around\s+(\d+)',
            r'about\s+(\d+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.lower())
            if match:
                groups = match.groups()
                if len(groups) == 1:
                    # Single price point
                    price = float(groups[0])
                    if 'under' in pattern or 'less' in pattern or 'below' in pattern:
                        return (0, price)
                    else:
                        return (price * 0.8, price * 1.2)  # ±20% range
                elif len(groups) == 2:
                    # Price range
                    return (float(groups[0]), float(groups[1]))
        
        return None
    
    def normalize_amenity_name(self, amenity: str) -> str:
        """
        Normalize amenity names to standard format
        """
        amenity_mappings = {
            'pool': 'swimming_pool',
            'swimming': 'swimming_pool',
            'swim': 'swimming_pool',
            'spa': 'spa',
            'massage': 'spa',
            'wellness': 'spa',
            'banquet': 'banquet',
            'conference': 'banquet',
            'meeting': 'banquet',
            'parking': 'parking',
            'valet': 'parking',
            'breakfast': 'breakfast',
            'morning meal': 'breakfast',
            'elevator': 'elevator',
            'lift': 'elevator',
            'laundry': 'laundry',
            'washing': 'laundry',
            'wifi': 'wifi',
            'internet': 'wifi',
            'wireless': 'wifi',
            'gym': 'gym',
            'fitness': 'gym',
            'exercise': 'gym',
            'restaurant': 'restaurant',
            'dining': 'restaurant',
            'food': 'restaurant',
            'room service': 'room_service',
            'ac': 'ac',
            'air conditioning': 'ac',
            'tv': 'tv',
            'television': 'tv',
            'twin bed': 'twin_bed',
            'double bed': 'double_bed',
            'balcony': 'balcony',
            'terrace': 'balcony',
            'view': 'view'
        }
        
        amenity_lower = amenity.lower().strip()
        
        # Direct mapping
        if amenity_lower in amenity_mappings:
            return amenity_mappings[amenity_lower]
        
        # Partial matching
        for key, value in amenity_mappings.items():
            if key in amenity_lower or amenity_lower in key:
                return value
        
        # Return normalized version if no mapping found
        return amenity_lower.replace(' ', '_')
    
    def is_hotel_name_query(self, text: str) -> bool:
        """
        Check if the query is asking about a specific hotel
        """
        hotel_indicators = [
            'treebo', 'hotel', 'resort', 'inn', 'suites', 'residency',
            'palace', 'grand', 'royal', 'premium', 'luxury'
        ]
        
        text_lower = text.lower()
        return any(indicator in text_lower for indicator in hotel_indicators)
    
    def extract_date_range(self, text: str) -> Optional[Tuple[str, str]]:
        """
        Extract date range from text (basic implementation)
        """
        # This is a basic implementation - you might want to use a more sophisticated
        # date parsing library like dateutil for production use
        
        date_patterns = [
            r'(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})',  # DD/MM/YYYY or DD-MM-YYYY
            r'(\d{2,4}[/-]\d{1,2}[/-]\d{1,2})',  # YYYY/MM/DD or YYYY-MM-DD
        ]
        
        dates = []
        for pattern in date_patterns:
            matches = re.findall(pattern, text)
            dates.extend(matches)
        
        if len(dates) >= 2:
            return (dates[0], dates[1])
        elif len(dates) == 1:
            return (dates[0], dates[0])
        
        return None
