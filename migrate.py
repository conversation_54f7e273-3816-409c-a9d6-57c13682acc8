#!/usr/bin/env python3
"""
Database Migration Runner for Hotel Portfolio Query Tool
Runs SQL migrations against PostgreSQL database
"""

import os
import sys
import psycopg2
import psycopg2.extras
from datetime import datetime
from config import Config

class MigrationRunner:
    def __init__(self):
        self.config = Config()
        self.connection = None
        self.migrations_dir = 'migrations'
        
    def connect(self):
        """Connect to PostgreSQL database"""
        try:
            self.connection = psycopg2.connect(
                host=self.config.DB_HOST,
                port=self.config.DB_PORT,
                database=self.config.DB_NAME,
                user=self.config.DB_USER,
                password=self.config.DB_PASSWORD
            )
            self.connection.autocommit = True
            print(f"✅ Connected to PostgreSQL database: {self.config.DB_NAME}")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def create_migrations_table(self):
        """Create migrations tracking table"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS schema_migrations (
                    id SERIAL PRIMARY KEY,
                    migration_name VARCHAR(255) UNIQUE NOT NULL,
                    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    execution_time_ms INTEGER,
                    status VARCHAR(50) DEFAULT 'success'
                )
            """)
            
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_schema_migrations_name 
                ON schema_migrations(migration_name)
            """)
            
            cursor.close()
            print("✅ Created schema_migrations table")
            return True
        except Exception as e:
            print(f"❌ Failed to create migrations table: {e}")
            return False
    
    def get_executed_migrations(self):
        """Get list of already executed migrations"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT migration_name FROM schema_migrations 
                WHERE status = 'success'
                ORDER BY migration_name
            """)
            executed = [row[0] for row in cursor.fetchall()]
            cursor.close()
            return executed
        except Exception as e:
            print(f"❌ Failed to get executed migrations: {e}")
            return []
    
    def get_pending_migrations(self):
        """Get list of pending migrations"""
        if not os.path.exists(self.migrations_dir):
            print(f"❌ Migrations directory not found: {self.migrations_dir}")
            return []
        
        # Get all migration files
        migration_files = [f for f in os.listdir(self.migrations_dir) 
                          if f.endswith('.sql')]
        migration_files.sort()
        
        # Get executed migrations
        executed = self.get_executed_migrations()
        
        # Find pending migrations
        pending = [f for f in migration_files if f not in executed]
        return pending
    
    def execute_migration(self, migration_file):
        """Execute a single migration file"""
        migration_path = os.path.join(self.migrations_dir, migration_file)
        
        try:
            # Read migration file
            with open(migration_path, 'r') as f:
                migration_sql = f.read()
            
            print(f"🔄 Executing migration: {migration_file}")
            start_time = datetime.now()
            
            # Execute migration
            cursor = self.connection.cursor()
            cursor.execute(migration_sql)
            cursor.close()
            
            # Calculate execution time
            end_time = datetime.now()
            execution_time = int((end_time - start_time).total_seconds() * 1000)
            
            # Record migration as executed
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO schema_migrations (migration_name, execution_time_ms)
                VALUES (%s, %s)
                ON CONFLICT (migration_name) DO UPDATE SET
                    executed_at = CURRENT_TIMESTAMP,
                    execution_time_ms = EXCLUDED.execution_time_ms,
                    status = 'success'
            """, (migration_file, execution_time))
            cursor.close()
            
            print(f"✅ Migration completed: {migration_file} ({execution_time}ms)")
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {migration_file} - {e}")
            
            # Record migration failure
            try:
                cursor = self.connection.cursor()
                cursor.execute("""
                    INSERT INTO schema_migrations (migration_name, status)
                    VALUES (%s, 'failed')
                    ON CONFLICT (migration_name) DO UPDATE SET
                        executed_at = CURRENT_TIMESTAMP,
                        status = 'failed'
                """, (migration_file,))
                cursor.close()
            except:
                pass
            
            return False
    
    def run_migrations(self):
        """Run all pending migrations"""
        if not self.connect():
            return False
        
        if not self.create_migrations_table():
            return False
        
        pending = self.get_pending_migrations()
        
        if not pending:
            print("✅ No pending migrations found. Database is up to date.")
            return True
        
        print(f"📋 Found {len(pending)} pending migrations:")
        for migration in pending:
            print(f"   - {migration}")
        
        print("\n🚀 Starting migration execution...")
        
        success_count = 0
        for migration in pending:
            if self.execute_migration(migration):
                success_count += 1
            else:
                print(f"❌ Stopping migration execution due to failure in: {migration}")
                break
        
        print(f"\n📊 Migration Summary:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Failed: {len(pending) - success_count}")
        
        if success_count == len(pending):
            print("🎉 All migrations completed successfully!")
            return True
        else:
            print("⚠️  Some migrations failed. Please check the errors above.")
            return False
    
    def show_status(self):
        """Show migration status"""
        if not self.connect():
            return False
        
        if not self.create_migrations_table():
            return False
        
        try:
            cursor = self.connection.cursor(cursor_factory=psycopg2.extras.DictCursor)
            cursor.execute("""
                SELECT migration_name, executed_at, execution_time_ms, status
                FROM schema_migrations 
                ORDER BY migration_name
            """)
            executed = cursor.fetchall()
            cursor.close()
            
            pending = self.get_pending_migrations()
            
            print("📊 Migration Status:")
            print("=" * 80)
            
            if executed:
                print("\n✅ Executed Migrations:")
                for migration in executed:
                    status_icon = "✅" if migration['status'] == 'success' else "❌"
                    print(f"   {status_icon} {migration['migration_name']} "
                          f"({migration['executed_at']}, {migration['execution_time_ms']}ms)")
            
            if pending:
                print(f"\n⏳ Pending Migrations ({len(pending)}):")
                for migration in pending:
                    print(f"   ⏳ {migration}")
            else:
                print("\n✅ No pending migrations. Database is up to date.")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to get migration status: {e}")
            return False
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()

def main():
    """Main migration runner"""
    runner = MigrationRunner()
    
    try:
        if len(sys.argv) > 1:
            command = sys.argv[1].lower()
            
            if command == 'status':
                runner.show_status()
            elif command == 'migrate':
                runner.run_migrations()
            else:
                print("Usage: python migrate.py [status|migrate]")
                print("  status  - Show migration status")
                print("  migrate - Run pending migrations")
        else:
            # Default action: run migrations
            runner.run_migrations()
    
    finally:
        runner.close()

if __name__ == '__main__':
    main()
