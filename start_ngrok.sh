#!/bin/bash

# Quick ngrok setup script for Hotel Portfolio Query Tool

echo "🏨 Hotel Portfolio Query Tool - ngrok Setup"
echo "============================================"

# Check if ngrok is configured
if ! ngrok config check > /dev/null 2>&1; then
    echo "⚠️  ngrok is not configured yet!"
    echo ""
    echo "📝 To configure ngrok:"
    echo "1. Sign up for free at: https://ngrok.com/"
    echo "2. Get your auth token from: https://dashboard.ngrok.com/get-started/your-authtoken"
    echo "3. Run: ngrok config add-authtoken YOUR_TOKEN"
    echo ""
    read -p "Press Enter after you've configured ngrok..."
fi

echo ""
echo "🚀 Starting your hotel search tool..."
echo ""

# Ask which app to run
echo "Which app would you like to run?"
echo "1. Simple App (demo with sample data) - Recommended for sharing"
echo "2. Full App (with database)"
read -p "Enter choice (1 or 2): " choice

if [ "$choice" = "1" ]; then
    APP_FILE="simple_app.py"
    echo "📱 Starting Simple App..."
else
    APP_FILE="app.py"
    echo "📱 Starting Full App..."
fi

# Start the Flask app in background
echo "🔧 Starting Flask application..."
python3 $APP_FILE &
FLASK_PID=$!

# Wait a moment for Flask to start
sleep 3

# Start ngrok
echo "🌐 Creating public tunnel with ngrok..."
ngrok http 5000 --log=stdout &
NGROK_PID=$!

# Wait a moment for ngrok to start
sleep 3

# Get the public URL
echo ""
echo "🔍 Getting your public URL..."
PUBLIC_URL=$(curl -s http://localhost:4040/api/tunnels | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    for tunnel in data['tunnels']:
        if tunnel['proto'] == 'https':
            print(tunnel['public_url'])
            break
except:
    pass
")

if [ ! -z "$PUBLIC_URL" ]; then
    echo ""
    echo "✅ SUCCESS! Your hotel search tool is now public!"
    echo "=================================================="
    echo ""
    echo "🌍 Public URL: $PUBLIC_URL"
    echo ""
    echo "📱 Share this URL with your colleague:"
    echo "   $PUBLIC_URL"
    echo ""
    echo "🔧 Available pages:"
    echo "   • Main Interface: $PUBLIC_URL/"
    echo "   • Dashboard: $PUBLIC_URL/dashboard"
    echo "   • Hotel Details: $PUBLIC_URL/hotel-details"
    echo "   • API Health: $PUBLIC_URL/health"
    echo ""
    echo "⚠️  SECURITY NOTE: This URL is public and accessible to anyone!"
    echo "🛑 Press Ctrl+C to stop both servers"
    echo ""
else
    echo "❌ Could not get public URL. Check ngrok configuration."
fi

# Wait for user to stop
trap "echo ''; echo '🛑 Stopping servers...'; kill $FLASK_PID $NGROK_PID 2>/dev/null; echo '✅ Servers stopped'; exit" INT

# Keep script running
while true; do
    sleep 1
done
