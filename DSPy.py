from inspect import signature

import dspy
import os

if __name__ == '__main__':
    from dspy import (Predict, ChainOfThought, Signature, InputField, OutputField,
                      ProgramOfThought)

    # Configure DSPy with a language model
    # Option 1: Using OpenAI (requires API key)
    # Uncomment the following lines and set your OpenAI API key
    # os.environ["OPENAI_API_KEY"] = "your-api-key-here"
    # lm = dspy.LM('openai/gpt-4o-mini')

    # Option 2: Using a local model (Ollama example)
    # Make sure you have Ollama installed and running
    # lm = dspy.LM('ollama/llama2', api_base='http://localhost:11434')

    # Option 3: Using a mock/dummy model for testing
    # This will use a simple pattern-based response
    lm = dspy.LM('openai/gpt-4o-mini',
                 api_key='********************************************************************************************************************************************************************')

    # Configure retrieval model (for dspy.Retrieve functionality)
    print("Setting up Retrieval Model...")

    # Option 1: ColBERTv2 with Wikipedia abstracts (hosted)
    try:
        print("Trying ColBERTv2 with Wikipedia abstracts...")
        rm = dspy.ColBERTv2(url='http://************:2017/wiki17_abstracts')
        print("✅ ColBERTv2 retrieval model loaded successfully!")
    except Exception as e:
        print(f"❌ ColBERTv2 failed: {e}")

        # Option 2: Try Weaviate (if available)
        try:
            print("Trying Weaviate retrieval...")
            rm = dspy.WeaviateRM("http://localhost:8080")
            print("✅ Weaviate retrieval model loaded successfully!")
        except Exception as e:
            print(f"❌ Weaviate failed: {e}")

            # Option 3: Try ChromaDB (local vector database)
            try:
                print("Trying ChromaDB retrieval...")
                import chromadb

                rm = dspy.ChromadbRM(collection_name="knowledge_base", persist_directory="./chroma_db")
                print("✅ ChromaDB retrieval model loaded successfully!")
            except Exception as e:
                print(f"❌ ChromaDB failed: {e}")

                # Option 4: Try Pinecone (cloud vector database)
                try:
                    print("Trying Pinecone retrieval...")
                    import pinecone

                    # You'll need to set PINECONE_API_KEY environment variable
                    rm = dspy.PineconeRM(index_name="knowledge-base")
                    print("✅ Pinecone retrieval model loaded successfully!")
                except Exception as e:
                    print(f"❌ Pinecone failed: {e}")
                    print("❌ All retrieval models failed. Please install and configure one of the following:")
                    print("1. ColBERTv2 server")
                    print("2. Weaviate server")
                    print("3. ChromaDB: pip install chromadb")
                    print("4. Pinecone: pip install pinecone-client")
                    raise Exception("No retrieval model available")

    # Configure DSPy with both language model and retrieval model
    dspy.configure(lm=lm, rm=rm)

    print("=== COMPARING Predict vs ChainOfThought ===\n")

    # Create both types of predictors
    basic_predictor = Predict("country -> capital")
    cot_predictor = ChainOfThought("country -> capital")

    # Test with a simple case first
    print("1. SIMPLE CASE - Well-known capitals:")
    print("-" * 40)

    country = "France"
    basic_result = basic_predictor(country=country)
    cot_result = cot_predictor(country=country)

    print(f"Country: {country}")
    print(f"Predict result: {basic_result.capital}")
    print(f"ChainOfThought result: {cot_result.capital}")
    if hasattr(cot_result, 'rationale'):
        print(f"ChainOfThought reasoning: {cot_result.rationale}")
    print()

    # Test with more complex cases where reasoning matters
    print("2. COMPLEX CASES - Where reasoning helps:")
    print("-" * 40)

    complex_cases = [
        "What is the capital of the country that has the Eiffel Tower?",
        "Which city serves as the capital of the nation known for sushi and Mount Fuji?",
        "What's the capital of the largest country in South America?",
        "Which country have Taj Mahal?"
    ]

    for case in complex_cases:
        print(f"Question: {case}")
        try:
            # For complex questions, we need to modify the signature
            complex_basic = Predict("question -> answer")
            complex_cot = ChainOfThought("question -> answer")

            basic_result = complex_basic(question=case)
            cot_result = complex_cot(question=case)

            print(f"Predict: {basic_result.answer}")
            print(f"ChainOfThought: {cot_result.answer}")
            if hasattr(cot_result, 'rationale'):
                print(f"Reasoning: {cot_result.rationale}")
            print()
        except Exception as e:
            print(f"Error with complex case: {e}")
            print()

    # Test with ambiguous cases
    print("3. AMBIGUOUS CASES - Multiple possible answers:")
    print("-" * 40)

    ambiguous_predictor = ChainOfThought("country_description -> capital, reasoning")

    ambiguous_cases = [
        "A country that was divided after WWII and reunified in 1990",
        "The country that spans two continents and has both European and Asian parts",
        "Which city is IT hub for India?"
    ]

    for case in ambiguous_cases:
        print(f"Description: {case}")
        try:
            result = ambiguous_predictor(country_description=case)
            print(f"Capital: {result.capital}")
            if hasattr(result, 'reasoning'):
                print(f"Reasoning: {result.reasoning}")
            print()
        except Exception as e:
            print(f"Error: {e}")
            print()

    print("4. ORIGINAL SIMPLE TEST:")
    print("-" * 40)
    countries = ["Germany", "Japan", "Brazil", "India"]
    for country in countries:
        result = cot_predictor(country=country)
        print(f"The capital of {country} is: {result.capital}")

    qa_module = Predict("destination -> recommended_activities, nearby_place_to_visit")
    destinations = [
        "Mumbai",
        "Delhi"
    ]
    print("5. NEXT SIMPLE TEST:")
    print("-" * 40)
    for dd in destinations:
        resp = qa_module(destination=dd)
        print(f"Q: {dd}\nA: {resp.recommended_activities}\n {resp.nearby_place_to_visit}")

    weather_module = ChainOfThought('city -> weather, temperature, is_rainy')

    cities = [
        "Jaipur",
        "chennai"
    ]
    print("6. NEXT SIMPLE TEST:")
    print("-" * 40)
    for cc in cities:
        resp = weather_module(city=cc)
        print(
            f"for City {cc}, we are getting to know mentioned info {resp.weather}, {resp.temperature}, {resp.is_rainy}\n\n")


    class ActivitySignature(Signature):
        destination = InputField(desc="Name of the place")
        activities = OutputField(desc="List of fun activities to do there")


    activity_finder = Predict(ActivitySignature)
    response = activity_finder(destination='Goa')
    print("\n7. NEXT SIMPLE TEST:")
    print("-" * 40)
    print(response.activities)


    class MathProblemSignature(Signature):
        math_problem = InputField(desc="Please define you math problem")
        solution = OutputField(desc="Solution for the given math problem")


    print("\n8. NEXT SIMPLE TEST:")
    print("-" * 40)
    math_tutor = ChainOfThought(MathProblemSignature)
    resp_1 = math_tutor(math_problem="If I have 3 apples and eat 1, then buy 5 more, how many do I have?")
    print(resp_1.solution)
    print('\n')
    resp_2 = math_tutor(math_problem=" if 5x-5=20+3y then what is possible values for x")
    print(resp_2.solution)


    class WordCountSignature(Signature):
        text = InputField(desc="Text for which work count is needed")
        word_count = OutputField(desc="Please count vowels in the given text")


    work_counter = ProgramOfThought(WordCountSignature)
    resp = work_counter(text="Help me get there")
    print(f'\n{resp.word_count}')

    print("\n9. NEXT SIMPLE TEST:")
    react = dspy.ReAct('question -> answer', tools=[
        dspy.Tool(
            name="count_floors",
            desc="Count the number of floor in burj khalifa",
            func=lambda question: 108
        )
    ])
    print(react(question="How many floors are in burj khalifa?"))


    # Real Citation Checker with retrieval model
    class CitationChecker(dspy.Module):
        def __init__(self):
            super().__init__()
            self.retriever = dspy.Retrieve(k=3)
            self.checker = dspy.ChainOfThought("question, context, answer -> supported : bool, reasoning: str")

        def forward(self, question, answer):
            # Retrieve relevant passages
            passages = self.retriever(question)

            # Extract text from passages - handle ColBERTv2 format
            try:
                if hasattr(passages, 'passages') and isinstance(passages.passages, list):
                    # ColBERTv2 format: Prediction object with passages list of strings
                    context = "\n\n".join(passages.passages)
                    print(f"✅ Successfully extracted {len(passages.passages)} passages from ColBERTv2")
                elif hasattr(passages, 'long_text'):
                    # Single passage object
                    context = passages.long_text
                elif isinstance(passages, list):
                    # List of passages or strings
                    context_parts = []
                    for p in passages:
                        if hasattr(p, 'long_text'):
                            context_parts.append(p.long_text)
                        elif hasattr(p, 'text'):
                            context_parts.append(p.text)
                        else:
                            context_parts.append(str(p))
                    context = "\n\n".join(context_parts)
                else:
                    # Fallback to string representation
                    context = str(passages)

            except Exception as e:
                print(f"Warning: Could not extract passages properly: {e}")
                context = f"Error retrieving context: {e}"

            # Check if answer is supported by retrieved context
            result = self.checker(question=question, context=context, answer=answer)
            return dspy.Prediction(
                supported=result.supported,
                reasoning=result.reasoning,
                context=context
            )


    print("\n10. CITATION CHECKING TEST WITH REAL RETRIEVAL:")
    print("-" * 50)
    try:
        citation_checker = CitationChecker()
        result = citation_checker(question="What is most famous place in India", answer="Taj Mahal")
        print(f"Question: What is most famous place in India")
        print(f"Answer: Taj Mahal")
        print(f"Is the answer supported? {result.supported}")
        print(f"Reasoning: {result.reasoning}")
        print(f"Retrieved context preview: {result.context[:200]}...")
    except Exception as e:
        print(f"Citation checker failed: {e}")
        print("This might be due to retrieval model configuration issues.")

    print("\n11 few shot learning")
    examples = [
        dspy.Example(text="I liked this movie much", sentiment="positive"),
        dspy.Example(text="Not so great day", sentiment="negetive"),
        dspy.Example(text="I might like thriller movie or not", sentiment="neutral")
    ]
    # Create a few-shot predictor
    predictor = dspy.Predict("text -> sentiment")

    # Attach examples
    predictor = predictor.fewshot(examples)
    
