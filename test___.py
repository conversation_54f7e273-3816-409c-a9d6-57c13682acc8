from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import padding
import base64

# ---- Config: Paths to PEM Files ----
PRIVATE_KEY_FILE = "aadhaar-integration-private.pem"
PUBLIC_KEY_FILE = "aadhaar-integration-public.pem"

# ---- Step 1: Load Private Key from PEM File ----
def load_private_key(path):
    with open(path, "rb") as key_file:
        return serialization.load_pem_private_key(
            key_file.read(),
            password=None  # If password-protected, replace with bytes
        )

# ---- Step 2: Load Public Key from PEM File ----
def load_public_key(path):
    with open(path, "rb") as key_file:
        return serialization.load_pem_public_key(key_file.read())

# ---- Step 3: Sign a Challenge using Private Key ----
def sign_challenge(private_key, challenge):
    signature = private_key.sign(
        challenge.encode(),
        padding.PKCS1v15(),  # Or use PSS if your keys use it
        hashes.SHA256()
    )
    return base64.b64encode(signature).decode()

# ---- Step 4: Verify Signature using Public Key ----
def verify_signature(public_key, challenge, signature_b64):
    try:
        public_key.verify(
            base64.b64decode(signature_b64),
            challenge.encode(),
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        return True
    except Exception as e:
        print("❌ Verification failed:", e)
        return False

# ---- Run Test ----
if __name__ == "__main__":
    challenge = "test-message-or-login-challenge"

    # Load keys
    private_key = load_private_key(PRIVATE_KEY_FILE)
    public_key = load_public_key(PUBLIC_KEY_FILE)

    # Sign
    signature = sign_challenge(private_key, challenge)
    print("🔏 Signature (Base64):", signature)

    # Verify
    is_valid = verify_signature(public_key, challenge, signature)
    print("✅ Keys are valid:", is_valid)
