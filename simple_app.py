#!/usr/bin/env python3
"""
Simplified Hotel Portfolio Query Tool for demonstration
"""

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import json
import logging
import os
import csv
from datetime import datetime
from config import Config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Global variable to store hotel data
HOTELS_DATA = []

def load_hotels_from_csv():
    """Load real hotel data from CSV file with basic info only"""
    hotels = []
    csv_file = 'hotel_metadata.csv'

    if not os.path.exists(csv_file):
        logger.warning(f"CSV file {csv_file} not found, using sample data")
        return SAMPLE_HOTELS

    try:
        with open(csv_file, 'r', encoding='utf-8') as file:
            reader = csv.reader(file)
            headers = next(reader)

            for values in reader:
                if len(values) < len(headers):
                    continue

                # Create a row dict manually to handle duplicate names
                row = {}
                for i, header in enumerate(headers):
                    if i < len(values):
                        row[header] = values[i]

                if row.get('status') == 'LIVE':  # Only include live hotels
                    # Get the actual hotel name (first 'name' column)
                    hotel_name = values[1]  # Second column is the hotel name
                    hotel = {
                        "property_id": int(row['property_id']),
                        "name": hotel_name,
                        "city_name": row['city_name'],
                        "locality_name": row.get('locality_name', ''),
                        "status": row['status'],
                        "postal_address": row.get('postal_address', ''),
                        "maps_link": row.get('maps_link', ''),
                        # No cached amenities - always fetch fresh from API
                        "property_amenities": {},
                        "room_amenities": {}
                    }
                    hotels.append(hotel)

        logger.info(f"Loaded {len(hotels)} hotels from CSV")
        return hotels

    except Exception as e:
        logger.error(f"Error loading CSV: {e}")
        return SAMPLE_HOTELS

def get_hotel_amenities_from_api(property_id):
    """Get fresh real-time amenity data from Treebo API with intelligent fallbacks"""
    try:
        import requests
        api_url = f"https://catalog.treebo.com/cataloging-service/api/v2/properties/?property_id={property_id}"

        # Get hotel info for intelligent fallbacks
        hotel_info = next((h for h in HOTELS_DATA if h['property_id'] == property_id), None)

        # Make fresh API call with shorter timeout for better performance
        response = requests.get(api_url, timeout=5)
        response.raise_for_status()

        data = response.json()
        if not data or not isinstance(data, list) or len(data) == 0:
            logger.warning(f"No data returned for property {property_id}")
            return apply_intelligent_fallbacks(hotel_info, {}, {})

        hotel_data = data[0]
        amenity_summary = hotel_data.get('amenity_summary', {})

        if not amenity_summary:
            logger.warning(f"No amenity_summary for property {property_id}")
            return apply_intelligent_fallbacks(hotel_info, {}, {})

        # Extract property amenities
        property_amenities = {}
        if 'property_amenities' in amenity_summary:
            for amenity_item in amenity_summary['property_amenities']:
                amenity_key = amenity_item.get('amenity_key', '')
                if amenity_key:
                    # Normalize amenity names
                    normalized_key = normalize_amenity_name(amenity_key)
                    property_amenities[normalized_key] = True
                    logger.debug(f"Property {property_id}: Found amenity {normalized_key}")

        # Extract room amenities by room type
        room_amenities = {}
        for room_type in ['OAK', 'MAHOGANY', 'ACACIA', 'MAPLE']:
            if room_type in amenity_summary:
                room_amenities[room_type.lower()] = {}
                for amenity_item in amenity_summary[room_type]:
                    amenity_key = amenity_item.get('amenity_key', '')
                    if amenity_key:
                        normalized_key = normalize_amenity_name(amenity_key)
                        room_amenities[room_type.lower()][normalized_key] = True

        # Apply intelligent fallbacks for missing amenities
        property_amenities, room_amenities = apply_intelligent_fallbacks(hotel_info, property_amenities, room_amenities)

        logger.info(f"Property {property_id}: Loaded {len(property_amenities)} property amenities, {len(room_amenities)} room types")
        return property_amenities, room_amenities

    except requests.exceptions.Timeout:
        logger.error(f"Timeout fetching amenities for property {property_id}")
        return apply_intelligent_fallbacks(hotel_info, {}, {})
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error fetching amenities for property {property_id}: {e}")
        return apply_intelligent_fallbacks(hotel_info, {}, {})
    except Exception as e:
        logger.error(f"Unexpected error fetching amenities for property {property_id}: {e}")
        return apply_intelligent_fallbacks(hotel_info, {}, {})

def create_brand_standards_table():
    """Create the brand_standards table to store Treebo and Itsy standard services"""
    try:
        import psycopg2
        from config import Config

        connection = psycopg2.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )

        connection.autocommit = True
        cursor = connection.cursor()

        # Create brand_standards table (PostgreSQL syntax)
        create_table_query = """
        CREATE TABLE IF NOT EXISTS brand_standards (
            id SERIAL PRIMARY KEY,
            brand_name VARCHAR(50) NOT NULL,
            amenity_key VARCHAR(100) NOT NULL,
            amenity_name VARCHAR(100) NOT NULL,
            description TEXT,
            category VARCHAR(50),
            guaranteed BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(brand_name, amenity_key)
        )
        """

        cursor.execute(create_table_query)

        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_brand_standards_brand ON brand_standards(brand_name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_brand_standards_amenity ON brand_standards(amenity_key)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_brand_standards_guaranteed ON brand_standards(guaranteed)")

        logger.info("Created brand_standards table successfully")

        # Insert Treebo standard services - Core guaranteed amenities
        treebo_standards = [
            ('treebo', 'free_breakfast', 'Free Breakfast', 'Complimentary breakfast for all guests', 'dining', True),
            ('treebo', 'geyser', 'Hot Water/Geyser', '24/7 hot water supply with geyser', 'basic_comfort', True),
            ('treebo', 'wifi', 'WiFi', 'Free high-speed WiFi internet', 'connectivity', True),
            ('treebo', 'free_wifi', 'Free WiFi', 'Complimentary WiFi internet access', 'connectivity', True),
        ]

        # Insert Itsy standard services - Core guaranteed amenities
        itsy_standards = [
            ('itsy', 'free_breakfast', 'Free Breakfast', 'Complimentary breakfast', 'dining', True),
            ('itsy', 'geyser', 'Hot Water/Geyser', 'Hot water supply with geyser', 'basic_comfort', True),
            ('itsy', 'wifi', 'WiFi', 'Free WiFi internet', 'connectivity', True),
            ('itsy', 'free_wifi', 'Free WiFi', 'Complimentary WiFi internet access', 'connectivity', True),
        ]

        # Insert all standards (PostgreSQL syntax with ON CONFLICT)
        insert_query = """
        INSERT INTO brand_standards
        (brand_name, amenity_key, amenity_name, description, category, guaranteed)
        VALUES (%s, %s, %s, %s, %s, %s)
        ON CONFLICT (brand_name, amenity_key) DO NOTHING
        """

        for standard in treebo_standards + itsy_standards:
            cursor.execute(insert_query, standard)

        logger.info(f"Inserted {cursor.rowcount} brand standard services")

        cursor.close()
        connection.close()

        return True

    except Exception as e:
        logger.error(f"Error creating brand_standards table: {e}")
        return False

def get_brand_standards_from_db(brand_name):
    """Get brand standard services from database"""
    try:
        import psycopg2
        import psycopg2.extras
        from config import Config

        connection = psycopg2.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )

        cursor = connection.cursor(cursor_factory=psycopg2.extras.DictCursor)

        query = """
        SELECT amenity_key, amenity_name, description, category, guaranteed
        FROM brand_standards
        WHERE brand_name = %s AND guaranteed = TRUE
        ORDER BY category, amenity_key
        """

        cursor.execute(query, (brand_name,))
        results = cursor.fetchall()

        cursor.close()
        connection.close()

        # Convert to the expected format
        standards = {}
        for row in results:
            standards[row['amenity_key']] = {
                'guaranteed': row['guaranteed'],
                'description': row['description'],
                'category': row['category'],
                'name': row['amenity_name']
            }

        return standards

    except Exception as e:
        logger.error(f"Error fetching brand standards from database: {e}")
        # Fallback to hardcoded values if database fails
        return get_fallback_brand_standards(brand_name)

def get_fallback_brand_standards(brand_name):
    """Fallback brand standards if database is unavailable"""
    if brand_name.lower() == 'treebo':
        return {
            'free_breakfast': {'guaranteed': True, 'description': 'Complimentary breakfast for all guests', 'category': 'dining'},
            'geyser': {'guaranteed': True, 'description': '24/7 hot water supply with geyser', 'category': 'basic_comfort'},
            'wifi': {'guaranteed': True, 'description': 'Free high-speed WiFi internet', 'category': 'connectivity'},
            'free_wifi': {'guaranteed': True, 'description': 'Complimentary WiFi internet access', 'category': 'connectivity'},
        }
    elif brand_name.lower() == 'itsy':
        return {
            'free_breakfast': {'guaranteed': True, 'description': 'Complimentary breakfast', 'category': 'dining'},
            'geyser': {'guaranteed': True, 'description': 'Hot water supply with geyser', 'category': 'basic_comfort'},
            'wifi': {'guaranteed': True, 'description': 'Free WiFi internet', 'category': 'connectivity'},
            'free_wifi': {'guaranteed': True, 'description': 'Complimentary WiFi internet access', 'category': 'connectivity'},
        }
    return {}

def get_all_brand_standard_amenities():
    """Get list of all brand standard amenities for easy reference"""
    try:
        import psycopg2
        from config import Config

        connection = psycopg2.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )

        cursor = connection.cursor()

        query = """
        SELECT DISTINCT amenity_key
        FROM brand_standards
        WHERE guaranteed = TRUE
        """

        cursor.execute(query)
        results = cursor.fetchall()

        cursor.close()
        connection.close()

        return [row[0] for row in results]

    except Exception as e:
        logger.error(f"Error fetching brand standard amenities: {e}")
        # Fallback to core standards
        return ['free_breakfast', 'geyser', 'wifi', 'free_wifi']

def initialize_brand_standards():
    """Initialize brand standards table on application startup"""
    try:
        success = create_brand_standards_table()
        if success:
            logger.info("Brand standards table initialized successfully")
        else:
            logger.warning("Failed to initialize brand standards table")
    except Exception as e:
        logger.error(f"Error initializing brand standards: {e}")

def apply_intelligent_fallbacks(hotel_info, property_amenities, room_amenities):
    """Apply intelligent fallbacks for amenities based on brand standards, location and hotel characteristics"""
    if not hotel_info:
        return property_amenities, room_amenities

    hotel_name = hotel_info.get('name', '').lower()
    hotel_city = hotel_info.get('city_name', '')

    # BRAND-BASED FALLBACKS - Most Important
    # Treebo brand standards - guaranteed services across entire portfolio
    if 'treebo' in hotel_name:
        treebo_standards = get_brand_standards_from_db('treebo')

        for amenity, service_info in treebo_standards.items():
            if service_info['guaranteed'] and not property_amenities.get(amenity, False):
                property_amenities[amenity] = True
                logger.info(f"Applied Treebo portfolio standard: {amenity} ({service_info['description']}) for {hotel_info['name']}")

    # Itsy Hotels brand standards
    elif 'itsy' in hotel_name:
        itsy_standards = get_brand_standards_from_db('itsy')

        for amenity, service_info in itsy_standards.items():
            if service_info['guaranteed'] and not property_amenities.get(amenity, False):
                property_amenities[amenity] = True
                logger.info(f"Applied Itsy portfolio standard: {amenity} ({service_info['description']}) for {hotel_info['name']}")

    # LOCATION-BASED FALLBACKS
    # Hill stations and mountain destinations - likely to have mountain views
    hill_stations = [
        'Shimla', 'Manali', 'Dharamshala', 'Dalhousie', 'Solan',  # Himachal Pradesh
        'Mussoorie', 'Nainital', 'Dehradun', 'Rishikesh', 'Haridwar',  # Uttarakhand
        'Darjeeling', 'Gangtok',  # West Bengal, Sikkim
        'Ooty', 'Kodaikanal', 'Yelagiri',  # Tamil Nadu
        'Munnar', 'Thekkady', 'Wayanad',  # Kerala
        'Coorg', 'Chikmagalur',  # Karnataka
        'Mahabaleshwar', 'Panchgani', 'Matheran', 'Lonavala',  # Maharashtra
        'Shillong',  # Meghalaya
        'Kumbhalgarh', 'Pushkar',  # Rajasthan
        'Srinagar', 'Jammu', 'Katra'  # J&K
    ]

    # Mountain view fallback logic
    if hotel_city in hill_stations:
        # Hotels in hill stations likely have mountain/valley views
        if not property_amenities.get('mountain_view', False):
            property_amenities['mountain_view'] = True
            logger.info(f"Applied mountain_view fallback for {hotel_info['name']} in {hotel_city}")

    # NAME-BASED FALLBACKS
    # Hotel name-based fallbacks for views
    view_keywords = ['mountain view', 'valley view', 'hill view', 'scenic view', 'mountain', 'valley', 'hills']
    if any(keyword in hotel_name for keyword in view_keywords):
        if not property_amenities.get('mountain_view', False):
            property_amenities['mountain_view'] = True
            logger.info(f"Applied mountain_view fallback based on hotel name: {hotel_info['name']}")

    # Premium hotel name indicators
    premium_keywords = ['premium', 'luxury', 'grand', 'palace', 'heritage', 'resort']
    if any(keyword in hotel_name for keyword in premium_keywords):
        premium_amenities = {
            'concierge': True,
            'room_service': True,
        }
        for amenity, should_have in premium_amenities.items():
            if should_have and not property_amenities.get(amenity, False):
                property_amenities[amenity] = True
                logger.info(f"Applied premium hotel amenity: {amenity} for {hotel_info['name']}")

    # UNIVERSAL BASIC AMENITIES
    # Basic amenities that most modern hotels should have
    basic_fallbacks = {
        'wifi': True,  # Most modern hotels have WiFi
        'geyser': True,  # Most hotels have hot water
    }

    for amenity, should_have in basic_fallbacks.items():
        if should_have and not property_amenities.get(amenity, False):
            property_amenities[amenity] = True
            logger.debug(f"Applied basic amenity fallback: {amenity} for {hotel_info['name']}")

    return property_amenities, room_amenities



def normalize_amenity_name(amenity_key):
    """Normalize amenity names to match our search keywords"""
    # Map API amenity keys to our standard names
    amenity_mapping = {
        # Treebo standard services
        'free_breakfast': 'free_breakfast',
        'complimentary_breakfast': 'free_breakfast',
        'breakfast': 'free_breakfast',
        'geyser': 'geyser',
        'hot_water': 'geyser',
        'water_heater': 'geyser',
        'wifi': 'wifi',
        'free_wifi': 'free_wifi',
        'internet': 'wifi',
        'wireless': 'wifi',
        'complimentary_wifi': 'free_wifi',
        'security': 'security',
        '24_hour_security': 'security',
        '24/7_security': 'security',
        'housekeeping': 'housekeeping',
        'daily_housekeeping': 'housekeeping',
        'laundry': 'laundry',
        'guest_laundry': 'laundry',
        'laundry_service': 'laundry',
        'front_desk': 'front_desk',
        '24_hour_front_desk': 'front_desk',
        'reception': 'front_desk',

        # Other amenities
        'parking': 'parking',
        'elevator': 'elevator',
        'lift': 'elevator',
        'ac_room': 'ac',
        'air_conditioning': 'ac',
        'flat_screen_tv': 'tv',
        'television': 'tv',
        'swimming_pool': 'swimming_pool',
        'pool': 'swimming_pool',
        'spa': 'spa',
        'gym': 'gym',
        'fitness_center': 'gym',
        'fitness': 'gym',
        'banquet': 'banquet',
        'conference_room': 'banquet',
        'meeting_room': 'banquet',
        'restaurant': 'restaurant',
        'dining': 'restaurant',
        'bar': 'bar',
        'lounge': 'bar',
        'pub': 'bar',
        'cocktail_bar': 'bar',
        'wine_bar': 'bar',
        'business_center': 'business_center',
        'business_centre': 'business_center',
        'concierge': 'concierge',
        'room_service': 'room_service',
        '24_hour_room_service': 'room_service'
    }

    return amenity_mapping.get(amenity_key, amenity_key)

# Sample hotel data for demonstration (fallback)
SAMPLE_HOTELS = [
    {
        "property_id": 1001,
        "name": "Treebo Paradise",
        "city_name": "Nagpur",
        "locality_name": "Sitabuldi",
        "status": "LIVE",
        "postal_address": "123 Central Avenue, Sitabuldi, Nagpur 440012",
        "maps_link": "https://maps.google.com/?q=21.1458,79.0882",
        "property_amenities": {
            "banquet": True,
            "parking": True,
            "restaurant": True,
            "wifi": True
        },
        "room_amenities": {
            "acacia": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "safe": True,
                "room_service": True
            },
            "oak": {
                "ac": True,
                "tv": True,
                "mini_bar": True,
                "wifi": True,
                "safe": True,
                "balcony": False
            }
        }
    },
    {
        "property_id": 1002,
        "name": "Treebo Grand Palace",
        "city_name": "Mumbai",
        "locality_name": "Andheri",
        "status": "LIVE",
        "postal_address": "456 SV Road, Andheri West, Mumbai 400058",
        "maps_link": "https://maps.google.com/?q=19.1136,72.8697",
        "property_amenities": {
            "swimming_pool": True,
            "spa": True,
            "parking": True,
            "restaurant": True,
            "gym": True,
            "wifi": True
        },
        "room_amenities": {
            "maple": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "room_service": True,
                "wifi": True,
                "mini_bar": True,
                "safe": True
            },
            "mahogany": {
                "ac": True,
                "tv": True,
                "mini_bar": True,
                "safe": True,
                "wifi": True,
                "balcony": True,
                "room_service": True
            }
        }
    },
    {
        "property_id": 1003,
        "name": "Treebo Royal Inn",
        "city_name": "Bangalore",
        "locality_name": "Koramangala",
        "status": "LIVE",
        "postal_address": "789 Koramangala 5th Block, Bangalore 560095",
        "maps_link": "https://maps.google.com/?q=12.9352,77.6245",
        "property_amenities": {
            "banquet": True,
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "elevator": True
        },
        "room_amenities": {
            "acacia": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "safe": True
            },
            "oak": {
                "ac": True,
                "tv": True,
                "room_service": True,
                "wifi": True,
                "mini_bar": True
            }
        }
    },
    {
        "property_id": 1004,
        "name": "Treebo Ocean View",
        "city_name": "Pondicherry",
        "locality_name": "White Town",
        "status": "LIVE",
        "postal_address": "654 Beach Road, White Town, Pondicherry 605001",
        "maps_link": "https://maps.google.com/?q=11.9416,79.8083",
        "property_amenities": {
            "swimming_pool": True,
            "restaurant": True,
            "wifi": True,
            "parking": True
        },
        "room_amenities": {
            "maple": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "room_service": True,
                "wifi": True,
                "safe": True
            },
            "acacia": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "mini_bar": False,
                "safe": True
            }
        }
    },
    {
        "property_id": 1005,
        "name": "Treebo Business Hotel",
        "city_name": "Pune",
        "locality_name": "Hinjewadi",
        "status": "LIVE",
        "postal_address": "987 IT Park Road, Hinjewadi, Pune 411057",
        "maps_link": "https://maps.google.com/?q=18.5912,73.7389",
        "property_amenities": {
            "banquet": True,
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "gym": True
        },
        "room_amenities": {
            "mahogany": {
                "ac": True,
                "tv": True,
                "mini_bar": True,
                "safe": True,
                "room_service": True,
                "wifi": True,
                "balcony": True
            },
            "oak": {
                "ac": True,
                "tv": True,
                "room_service": True,
                "wifi": True,
                "safe": True,
                "balcony": False
            }
        }
    },
    {
        "property_id": 1006,
        "name": "Treebo Mountain View Resort",
        "city_name": "Shimla",
        "locality_name": "Mall Road",
        "status": "LIVE",
        "postal_address": "456 Mall Road, Shimla, Himachal Pradesh 171001",
        "maps_link": "https://maps.google.com/?q=31.1048,77.1734",
        "property_amenities": {
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "elevator": True,
            "mountain_view": True
        },
        "room_amenities": {
            "acacia": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "safe": True,
                "room_service": True,
                "mountain_view": True
            },
            "maple": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "mini_bar": True,
                "safe": True,
                "mountain_view": True
            }
        }
    },
    {
        "property_id": 1007,
        "name": "Treebo Valley Resort",
        "city_name": "Manali",
        "locality_name": "Old Manali",
        "status": "LIVE",
        "postal_address": "789 Old Manali Road, Manali, Himachal Pradesh 175131",
        "maps_link": "https://maps.google.com/?q=32.2396,77.1887",
        "property_amenities": {
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "spa": True
        },
        "room_amenities": {
            "oak": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "safe": True,
                "room_service": True
            },
            "mahogany": {
                "ac": True,
                "tv": True,
                "balcony": True,
                "wifi": True,
                "mini_bar": True,
                "safe": True,
                "room_service": True
            }
        }
    },
    {
        "property_id": 1008,
        "name": "Treebo Business Center",
        "city_name": "Bangalore",
        "locality_name": "Electronic City",
        "status": "LIVE",
        "postal_address": "456 Electronic City Phase 1, Bangalore 560100",
        "maps_link": "https://maps.google.com/?q=12.8456,77.6603",
        "property_amenities": {
            "banquet": True,
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "gym": True
        },
        "room_amenities": {
            "oak": {
                "ac": True,
                "tv": True,
                "wifi": True,
                "safe": True,
                "room_service": True,
                "mini_bar": True
            },
            "mahogany": {
                "ac": True,
                "tv": True,
                "wifi": True,
                "safe": True,
                "room_service": True,
                "mini_bar": True,
                "balcony": True
            }
        }
    },
    {
        "property_id": 1009,
        "name": "Treebo Corporate Hub",
        "city_name": "Bangalore",
        "locality_name": "Whitefield",
        "status": "LIVE",
        "postal_address": "789 ITPL Road, Whitefield, Bangalore 560066",
        "maps_link": "https://maps.google.com/?q=12.9698,77.7500",
        "property_amenities": {
            "banquet": True,
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "elevator": True
        },
        "room_amenities": {
            "acacia": {
                "ac": True,
                "tv": True,
                "wifi": True,
                "safe": True,
                "room_service": True
            },
            "maple": {
                "ac": True,
                "tv": True,
                "wifi": True,
                "safe": True,
                "room_service": True,
                "balcony": True
            }
        }
    },
    {
        "property_id": 1010,
        "name": "Treebo Tech Park Hotel",
        "city_name": "Bangalore",
        "locality_name": "Koramangala",
        "status": "LIVE",
        "postal_address": "123 Koramangala 5th Block, Bangalore 560095",
        "maps_link": "https://maps.google.com/?q=12.9352,77.6245",
        "property_amenities": {
            "parking": True,
            "restaurant": True,
            "wifi": True,
            "gym": True
        },
        "room_amenities": {
            "oak": {
                "ac": True,
                "tv": True,
                "wifi": True,
                "safe": True,
                "room_service": True
            }
        }
    }
]

def get_city_aliases():
    """Comprehensive city alias mapping to official database names"""
    return {
        # Major Metro Cities
        'hyderabad': ['Hyderabad'],
        'secunderabad': ['Hyderabad'],
        'cyberabad': ['Hyderabad'],
        'hitec city': ['Hyderabad'],
        'hitech city': ['Hyderabad'],

        'bangalore': ['Bangalore'],
        'bengaluru': ['Bangalore'],
        'blr': ['Bangalore'],

        'mumbai': ['Mumbai'],
        'bombay': ['Mumbai'],
        'bom': ['Mumbai'],

        'delhi': ['Delhi', 'New Delhi'],
        'new delhi': ['New Delhi', 'Delhi'],
        'ncr': ['Delhi', 'New Delhi', 'Gurgaon', 'Noida', 'Faridabad'],
        'gurgaon': ['Gurgaon'],
        'gurugram': ['Gurgaon'],
        'ggn': ['Gurgaon'],
        'noida': ['Noida'],
        'faridabad': ['Faridabad'],

        'chennai': ['Chennai'],
        'madras': ['Chennai'],
        'maa': ['Chennai'],

        'kolkata': ['Kolkata'],
        'calcutta': ['Kolkata'],
        'cal': ['Kolkata'],

        'pune': ['Pune'],
        'poona': ['Pune'],

        # Goa and surrounding
        'goa': ['Goa'],
        'panaji': ['Goa'],
        'panjim': ['Goa'],
        'margao': ['Goa'],
        'madgaon': ['Goa'],
        'vasco': ['Goa'],
        'vasco da gama': ['Goa'],

        # Kerala Cities
        'kochi': ['Kochi'],
        'cochin': ['Kochi'],
        'ernakulam': ['Kochi'],
        'thiruvananthapuram': ['Thiruvananthapuram'],
        'trivandrum': ['Thiruvananthapuram'],
        'tvm': ['Thiruvananthapuram'],
        'kozhikode': ['Kozhikode'],
        'calicut': ['Kozhikode'],
        'thrissur': ['Thrissur'],
        'trichur': ['Thrissur'],
        'kannur': ['Kannur'],
        'cannanore': ['Kannur'],
        'kottayam': ['Kottayam'],
        'munnar': ['Munnar'],
        'alleppey': ['Alleppey'],
        'alappuzha': ['Alleppey'],
        'wayanad': ['Wayanad'],
        'varkala': ['Varkala'],
        'guruvayur': ['Guruvayur'],
        'thekkady': ['Thekkady'],

        # Tamil Nadu Cities
        'coimbatore': ['Coimbatore'],
        'cbe': ['Coimbatore'],
        'madurai': ['Madurai'],
        'thanjavur': ['Thanjavur'],
        'tanjore': ['Thanjavur'],
        'tirupati': ['Tirupati'],
        'vellore': ['Vellore'],
        'kodaikanal': ['Kodaikanal'],
        'ooty': ['Ooty'],
        'ootacamund': ['Ooty'],
        'udhagamandalam': ['Ooty'],
        'velankanni': ['Velankanni'],
        'yelagiri': ['Yelagiri'],

        # Karnataka Cities
        'mysore': ['Mysore'],
        'mysuru': ['Mysore'],
        'mangalore': ['Mangalore'],
        'mangaluru': ['Mangalore'],
        'belgaum': ['Belgaum'],
        'belagavi': ['Belgaum'],
        'hassan': ['Hassan'],
        'chikmagalur': ['Chikmagalur'],
        'chikkamagaluru': ['Chikmagalur'],
        'coorg': ['Coorg'],
        'kodagu': ['Coorg'],
        'manipal': ['Manipal'],
        'udupi': ['Udupi'],

        # Andhra Pradesh & Telangana
        'visakhapatnam': ['Visakhapatnam'],
        'vizag': ['Visakhapatnam'],
        'vijayawada': ['Vijayawada'],
        'kakinada': ['Kakinada'],

        # Gujarat Cities
        'ahmedabad': ['Ahmedabad'],
        'amdavad': ['Ahmedabad'],
        'gandhinagar': ['Gandhinagar'],
        'surat': ['Surat'],
        'rajkot': ['Rajkot'],
        'vadodara': ['Vadodara'],
        'baroda': ['Vadodara'],
        'vapi': ['Vapi'],

        # Rajasthan Cities
        'jaipur': ['Jaipur'],
        'udaipur': ['Udaipur'],
        'jodhpur': ['Jodhpur'],
        'ajmer': ['Ajmer'],
        'pushkar': ['Pushkar'],
        'bikaner': ['Bikaner'],
        'jaisalmer': ['Jaisalmer'],
        'kumbhalgarh': ['Kumbhalgarh'],
        'osian': ['Osian'],

        # Maharashtra Cities
        'nashik': ['Nashik'],
        'nasik': ['Nashik'],
        'nagpur': ['Nagpur'],
        'aurangabad': ['Aurangabad'],
        'kolhapur': ['Kolhapur'],
        'satara': ['Satara'],
        'lonavala': ['Lonavala'],
        'lonavla': ['Lonavala'],
        'mahabaleshwar': ['Mahabaleshwar'],
        'panchgani': ['Panchgani'],
        'matheran': ['Matheran'],
        'dapoli': ['Dapoli'],
        'amravati': ['Amravati'],
        'chandrapur': ['Chandrapur'],

        # Uttar Pradesh Cities
        'lucknow': ['Lucknow'],
        'agra': ['Agra'],
        'varanasi': ['Varanasi'],
        'banaras': ['Varanasi'],
        'kashi': ['Varanasi'],
        'mathura': ['Mathura'],
        'vrindavan': ['Vrindavan'],
        'prayagraj': ['Prayagraj'],
        'allahabad': ['Prayagraj'],
        'ayodhya': ['Ayodhya'],

        # Madhya Pradesh Cities
        'bhopal': ['Bhopal'],
        'indore': ['Indore'],
        'ujjain': ['Ujjain'],
        'pench': ['Pench'],

        # West Bengal Cities
        'darjeeling': ['Darjeeling'],
        'siliguri': ['Siliguri'],
        'durgapur': ['Durgapur'],

        # Himachal Pradesh Cities
        'shimla': ['Shimla'],
        'simla': ['Shimla'],
        'manali': ['Manali'],
        'dharamshala': ['Dharamshala'],
        'dharamsala': ['Dharamshala'],
        'dalhousie': ['Dalhousie'],
        'solan': ['Solan'],

        # Uttarakhand Cities
        'dehradun': ['Dehradun'],
        'mussoorie': ['Mussoorie'],
        'rishikesh': ['Rishikesh'],
        'haridwar': ['Haridwar'],
        'hardwar': ['Haridwar'],
        'nainital': ['Nainital'],
        'haldwani': ['Haldwani'],
        'roorkee': ['Roorkee'],
        'ramnagar': ['Ramnagar'],

        # Punjab & Haryana Cities
        'chandigarh': ['Chandigarh'],
        'amritsar': ['Amritsar'],
        'jalandhar': ['Jalandhar'],
        'ludhiana': ['Ludhiana'],
        'ambala': ['Ambala'],
        'manesar': ['Manesar'],
        'zirakpur': ['Zirakpur'],

        # Odisha Cities
        'bhubaneswar': ['Bhubaneswar'],
        'puri': ['Puri'],

        # Jharkhand & Bihar Cities
        'ranchi': ['Ranchi'],
        'jamshedpur': ['Jamshedpur'],
        'patna': ['Patna'],

        # Chhattisgarh Cities
        'raipur': ['Raipur'],

        # Assam Cities
        'guwahati': ['Guwahati'],
        'gauhati': ['Guwahati'],

        # Meghalaya Cities
        'shillong': ['Shillong'],

        # Sikkim Cities
        'gangtok': ['Gangtok'],

        # Jammu & Kashmir Cities
        'srinagar': ['Srinagar'],
        'jammu': ['Jammu'],
        'katra': ['Katra'],

        # Pondicherry
        'pondicherry': ['Pondicherry'],
        'puducherry': ['Pondicherry'],
        'pondy': ['Pondicherry'],

        # Other Cities
        'bhimtal': ['Bhimtal'],
        'baddi': ['Baddi'],
        'bhilwara': ['Bhilwara'],
        'gummidipoondi': ['Gummidipoondi'],
        'manjeri': ['Manjeri']
    }

def get_state_city_mappings():
    """Map states and regions to their cities in our database"""
    return {
        # States
        'himachal pradesh': ['Shimla', 'Manali', 'Dharamshala', 'Dalhousie', 'Solan'],
        'himachal': ['Shimla', 'Manali', 'Dharamshala', 'Dalhousie', 'Solan'],
        'hp': ['Shimla', 'Manali', 'Dharamshala', 'Dalhousie', 'Solan'],

        'maharashtra': ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Kolhapur', 'Satara', 'Lonavala', 'Mahabaleshwar', 'Panchgani', 'Matheran', 'Dapoli', 'Amravati', 'Chandrapur'],

        'karnataka': ['Bangalore', 'Mysore', 'Mangalore', 'Belgaum', 'Hassan', 'Chikmagalur', 'Coorg', 'Manipal', 'Udupi'],

        'tamil nadu': ['Chennai', 'Coimbatore', 'Madurai', 'Thanjavur', 'Tirupati', 'Vellore', 'Kodaikanal', 'Ooty', 'Velankanni', 'Yelagiri'],
        'tn': ['Chennai', 'Coimbatore', 'Madurai', 'Thanjavur', 'Tirupati', 'Vellore', 'Kodaikanal', 'Ooty', 'Velankanni', 'Yelagiri'],

        'kerala': ['Kochi', 'Thiruvananthapuram', 'Kozhikode', 'Thrissur', 'Kannur', 'Kottayam', 'Munnar', 'Alleppey', 'Wayanad', 'Varkala', 'Guruvayur', 'Thekkady'],

        'rajasthan': ['Jaipur', 'Udaipur', 'Jodhpur', 'Ajmer', 'Pushkar', 'Bikaner', 'Jaisalmer', 'Kumbhalgarh', 'Osian'],

        'gujarat': ['Ahmedabad', 'Gandhinagar', 'Surat', 'Rajkot', 'Vadodara', 'Vapi'],

        'west bengal': ['Kolkata', 'Darjeeling', 'Siliguri', 'Durgapur'],
        'wb': ['Kolkata', 'Darjeeling', 'Siliguri', 'Durgapur'],

        'telangana': ['Hyderabad'],

        'andhra pradesh': ['Visakhapatnam', 'Vijayawada', 'Kakinada'],
        'ap': ['Visakhapatnam', 'Vijayawada', 'Kakinada'],

        'uttar pradesh': ['Lucknow', 'Agra', 'Varanasi', 'Mathura', 'Vrindavan', 'Prayagraj', 'Ayodhya'],
        'up': ['Lucknow', 'Agra', 'Varanasi', 'Mathura', 'Vrindavan', 'Prayagraj', 'Ayodhya'],

        'madhya pradesh': ['Bhopal', 'Indore', 'Ujjain', 'Pench'],
        'mp': ['Bhopal', 'Indore', 'Ujjain', 'Pench'],

        'uttarakhand': ['Dehradun', 'Mussoorie', 'Rishikesh', 'Haridwar', 'Nainital', 'Haldwani', 'Roorkee', 'Ramnagar'],

        'punjab': ['Chandigarh', 'Amritsar', 'Jalandhar', 'Ludhiana'],

        'haryana': ['Gurgaon', 'Faridabad', 'Ambala', 'Manesar', 'Zirakpur'],

        'odisha': ['Bhubaneswar', 'Puri'],
        'orissa': ['Bhubaneswar', 'Puri'],

        'jharkhand': ['Ranchi', 'Jamshedpur'],

        'bihar': ['Patna'],

        'chhattisgarh': ['Raipur'],

        'assam': ['Guwahati'],

        'meghalaya': ['Shillong'],

        'sikkim': ['Gangtok'],

        'jammu and kashmir': ['Srinagar', 'Jammu', 'Katra'],
        'j&k': ['Srinagar', 'Jammu', 'Katra'],

        'pondicherry': ['Pondicherry'],
        'puducherry': ['Pondicherry'],

        # Regions
        'south india': ['Bangalore', 'Chennai', 'Hyderabad', 'Kochi', 'Thiruvananthapuram', 'Coimbatore', 'Mysore', 'Mangalore'],
        'north india': ['Delhi', 'New Delhi', 'Mumbai', 'Pune', 'Jaipur', 'Agra', 'Lucknow', 'Chandigarh'],
        'western ghats': ['Munnar', 'Coorg', 'Ooty', 'Mahabaleshwar', 'Lonavala'],
        'hill stations': ['Shimla', 'Manali', 'Dharamshala', 'Mussoorie', 'Nainital', 'Ooty', 'Munnar', 'Kodaikanal', 'Darjeeling'],
        'beaches': ['Goa', 'Varkala', 'Alleppey', 'Dapoli'],
        'backwaters': ['Alleppey', 'Kottayam', 'Kochi']
    }

def extract_location_from_query(query):
    """Extract location keywords from natural language query with comprehensive alias support"""
    query_lower = query.lower()
    found_locations = []

    # Get city aliases mapping
    city_aliases = get_city_aliases()

    # Check for city aliases in query (exact word matching)
    for alias, official_names in city_aliases.items():
        # Use word boundary matching to avoid partial matches
        import re
        if re.search(r'\b' + re.escape(alias) + r'\b', query_lower):
            found_locations.extend(official_names)

    # Check for state/region mentions
    state_mappings = get_state_city_mappings()

    for state, state_cities in state_mappings.items():
        if re.search(r'\b' + re.escape(state) + r'\b', query_lower):
            found_locations.extend(state_cities)

    return list(set(found_locations))  # Remove duplicates

def is_location_match(hotel, location_keywords):
    """Check if hotel matches any of the location keywords with strict city matching"""
    if not location_keywords:
        return True  # No location specified, match all

    hotel_city = hotel['city_name']
    hotel_locality = hotel.get('locality_name', '')

    for location in location_keywords:
        # Exact city match (case insensitive) - most important
        if location.lower() == hotel_city.lower():
            return True

        # Check if hotel city is in the location keyword (for state searches)
        # But be more strict to avoid false matches
        if len(location) > 3 and location.lower() in hotel_city.lower():
            return True

        # Check locality only for exact matches to avoid false positives
        if hotel_locality and location.lower() == hotel_locality.lower():
            return True

    return False

def categorize_amenities():
    """Categorize amenities by importance and rarity"""
    return {
        'basic_property': {
            # Treebo Standard Services - Guaranteed across portfolio
            'free_breakfast': ['free breakfast', 'breakfast', 'complimentary breakfast'],
            'geyser': ['geyser', 'hot water', 'water heater'],
            'wifi': ['wifi', 'internet', 'wireless'],
            'free_wifi': ['free wifi', 'complimentary wifi', 'free internet'],
            'security': ['security', '24 hour security', '24/7 security'],
            'housekeeping': ['housekeeping', 'daily housekeeping'],
            'laundry': ['laundry', 'guest laundry', 'laundry service'],
            'front_desk': ['front desk', '24 hour front desk', 'reception'],

            # Other basic amenities
            'restaurant': ['restaurant', 'dining', 'food'],
            'elevator': ['elevator', 'lift']
        },
        'premium_property': {
            'parking': ['parking', 'car park', 'vehicle parking'],
            'swimming_pool': ['swimming pool', 'pool', 'swimming'],
            'spa': ['spa', 'massage', 'wellness'],
            'gym': ['gym', 'fitness', 'workout'],
            'banquet': ['banquet', 'conference', 'meeting', 'event'],
            'mountain_view': ['mountain view', 'mountain', 'hills', 'valley view', 'scenic view'],
            'bar': ['bar', 'lounge', 'pub', 'cocktail bar', 'wine bar'],
            'business_center': ['business center', 'business centre', 'work space'],
            'concierge': ['concierge', 'front desk', 'reception'],
            'room_service': ['room service', '24 hour room service']
        },
        'basic_room': {
            'ac': ['ac', 'air conditioning', 'air conditioned'],
            'tv': ['tv', 'television'],
            'wifi': ['wifi', 'internet', 'wireless']
        },
        'premium_room': {
            'balcony': ['balcony', 'terrace'],
            'mini_bar': ['mini bar', 'minibar', 'mini-bar'],
            'safe': ['safe', 'locker'],
            'mountain_view': ['mountain view', 'mountain', 'hills', 'valley view']
        }
    }

def extract_amenities_from_query(query):
    """Extract amenity keywords from query with intelligence"""
    amenity_categories = categorize_amenities()
    query_lower = query.lower()

    found_amenities = {
        'basic_property': [],
        'premium_property': [],
        'basic_room': [],
        'premium_room': []
    }

    # Check each category
    for category, amenities in amenity_categories.items():
        for amenity, variations in amenities.items():
            for variation in variations:
                if variation in query_lower:
                    found_amenities[category].append(amenity)
                    break

    return found_amenities

def get_intelligent_search_context(query):
    """Analyze query to understand user intent and provide intelligent context"""
    query_lower = query.lower()

    # Detect search intent
    intent = {
        'location_focused': any(word in query_lower for word in ['in', 'at', 'near', 'around']),
        'amenity_focused': any(word in query_lower for word in ['with', 'having', 'includes']),
        'luxury_focused': any(word in query_lower for word in ['luxury', 'premium', 'deluxe', 'spa', 'mountain view']),
        'business_focused': any(word in query_lower for word in ['business', 'conference', 'meeting', 'banquet']),
        'budget_focused': any(word in query_lower for word in ['budget', 'cheap', 'affordable', 'basic'])
    }

    # Detect specific needs
    special_needs = {
        'parking_required': any(word in query_lower for word in ['parking', 'car']),
        'view_important': any(word in query_lower for word in ['view', 'mountain', 'scenic']),
        'wellness_focused': any(word in query_lower for word in ['spa', 'wellness', 'massage']),
        'fitness_focused': any(word in query_lower for word in ['gym', 'fitness']),
        'event_planning': any(word in query_lower for word in ['event', 'conference', 'meeting'])
    }

    return intent, special_needs

def calculate_intelligent_score(hotel, query, location_keywords):
    """Calculate intelligent scoring based on amenity importance and user intent"""
    intent, special_needs = get_intelligent_search_context(query)
    found_amenities = extract_amenities_from_query(query)

    match_score = 0
    matched_amenities = []
    confidence_factors = []

    # Location scoring (highest priority)
    if location_keywords:
        if is_location_match(hotel, location_keywords):
            match_score += 0.4
            confidence_factors.append("location_match")
        else:
            return 0, [], []  # No match if location doesn't match

    # Premium property amenities (high value)
    for amenity in found_amenities['premium_property']:
        if hotel['property_amenities'].get(amenity, False):
            if amenity in ['spa', 'mountain_view', 'swimming_pool']:
                # Give higher score when no location is specified (amenity-focused search)
                amenity_score = 0.45 if not location_keywords else 0.25
                match_score += amenity_score  # High value amenities
            else:
                amenity_score = 0.35 if not location_keywords else 0.2
                match_score += amenity_score   # Medium value amenities
            matched_amenities.append(amenity)
            confidence_factors.append(f"premium_property_{amenity}")

    # Basic property amenities (lower value, but still counted)
    basic_property_matches = 0
    for amenity in found_amenities['basic_property']:
        if hotel['property_amenities'].get(amenity, False):
            basic_property_matches += 1
            matched_amenities.append(amenity)

    # Only give points for basic amenities if user specifically asks for them
    if basic_property_matches > 0 and intent['amenity_focused']:
        match_score += 0.1 * (basic_property_matches / len(found_amenities['basic_property']))
        confidence_factors.append("basic_property_amenities")

    # Premium room amenities
    for room_type, room_amenities in hotel['room_amenities'].items():
        for amenity in found_amenities['premium_room']:
            if room_amenities.get(amenity, False):
                match_score += 0.15
                matched_amenities.append(f"{room_type}_{amenity}")
                confidence_factors.append(f"premium_room_{amenity}")

    # Special intent bonuses
    if intent['luxury_focused'] and any(amenity in hotel['property_amenities'] for amenity in ['spa', 'mountain_view']):
        match_score += 0.1
        confidence_factors.append("luxury_intent_match")

    if intent['business_focused'] and hotel['property_amenities'].get('banquet', False):
        match_score += 0.2  # Extra points for business hotels with banquet facilities
        matched_amenities.append('banquet')
        confidence_factors.append("business_intent_match")

    if special_needs['parking_required'] and hotel['property_amenities'].get('parking', False):
        match_score += 0.15  # Extra points for parking when specifically needed
        confidence_factors.append("parking_need_met")

    if special_needs['view_important'] and hotel['property_amenities'].get('mountain_view', False):
        match_score += 0.2   # Extra points for view when important
        confidence_factors.append("view_need_met")

    # Hotel name relevance
    hotel_words = hotel['name'].lower().split()
    query_words = query.split()
    name_matches = sum(1 for word in query_words if any(word in hotel_word for hotel_word in hotel_words))
    if name_matches > 0:
        match_score += 0.2 * (name_matches / len(query_words))
        confidence_factors.append("name_match")

    return min(match_score, 1.0), matched_amenities, confidence_factors

@app.route('/')
def index():
    """Render the main interface with AI model highlighting"""
    return render_template('dashboard.html')

@app.route('/dashboard')
def dashboard():
    """Render the advanced search dashboard"""
    return render_template('dashboard.html')

@app.route('/hotel-details')
def hotel_details():
    """Render the detailed hotel view"""
    return render_template('hotel_details.html')

@app.route('/api/query', methods=['POST'])
def process_query():
    """Process natural language queries"""
    global HOTELS_DATA
    try:
        data = request.get_json()
        query = data.get('query', '').strip().lower()

        if not query:
            return jsonify({
                'error': 'Query cannot be empty',
                'status': 'error'
            }), 400

        logger.info(f"Processing query: {query}")

        # Enhanced query processing with configurable confidence filtering
        results = []
        HIGH_CONFIDENCE_THRESHOLD = Config.MEDIUM_CONFIDENCE_THRESHOLD  # Use configurable threshold

        # Extract location information from query
        location_keywords = extract_location_from_query(query)

        # First filter hotels by location if specified
        if location_keywords:
            logger.info(f"Filtering hotels for locations: {location_keywords}")
            filtered_hotels = []
            for hotel in HOTELS_DATA:
                if is_location_match(hotel, location_keywords):
                    filtered_hotels.append(hotel)
                    logger.debug(f"✅ {hotel['name']} in {hotel['city_name']} matches location filter")
                else:
                    logger.debug(f"❌ {hotel['name']} in {hotel['city_name']} does NOT match location filter")

            logger.info(f"Filtered to {len(filtered_hotels)} hotels")
            if len(filtered_hotels) > 0:
                sample_cities = list(set([h['city_name'] for h in filtered_hotels[:10]]))
                logger.info(f"Sample cities in filtered results: {sample_cities}")

            if not filtered_hotels:
                # No hotels found in specified location
                return jsonify({
                    'query': query,
                    'result': {
                        'results': [],
                        'total_results': 0,
                        'summary': f"No hotels found in the specified location: {', '.join(location_keywords)}. Available cities: {', '.join(set(h['city_name'] for h in HOTELS_DATA))}",
                        'ai_powered': False
                    },
                    'status': 'success'
                })
        else:
            filtered_hotels = HOTELS_DATA

        # Extract amenities from query to check if specific amenities are requested
        found_amenities = extract_amenities_from_query(query)
        amenities_requested = any(found_amenities[category] for category in found_amenities)

        # If amenities are requested, load fresh amenity data for location-filtered hotels
        hotels_with_amenities = []
        if amenities_requested and filtered_hotels:
            logger.info(f"Loading fresh amenities for {len(filtered_hotels)} hotels")

            # Determine how many hotels to check based on amenity type
            # For brand standard services, check all hotels since they're guaranteed across portfolio
            brand_standard_amenities = get_all_brand_standard_amenities()
            requested_amenity_list = []
            for category in found_amenities.values():
                requested_amenity_list.extend(category)

            is_brand_standard = any(amenity in brand_standard_amenities for amenity in requested_amenity_list)

            if is_brand_standard and len(filtered_hotels) <= 100:
                # For brand standard services, check all hotels since they're guaranteed across portfolio
                hotels_to_check = filtered_hotels
                matching_standards = [a for a in requested_amenity_list if a in brand_standard_amenities]
                logger.info(f"Checking all {len(hotels_to_check)} hotels for brand standard service: {matching_standards}")
            else:
                # For rare/premium amenities or large cities, limit to first 30 hotels
                hotels_to_check = filtered_hotels[:30]
                logger.info(f"Checking first {len(hotels_to_check)} hotels for premium/rare amenities")

            # Load fresh amenities for each hotel
            for hotel in hotels_to_check:
                logger.info(f"Fetching amenities for hotel {hotel['property_id']}: {hotel['name']}")
                property_amenities, room_amenities = get_hotel_amenities_from_api(hotel['property_id'])
                hotel['property_amenities'] = property_amenities
                hotel['room_amenities'] = room_amenities
                hotels_with_amenities.append(hotel)
        else:
            hotels_with_amenities = filtered_hotels

        # Search for hotels based on query using intelligent scoring
        for hotel in hotels_with_amenities:

            # Use intelligent scoring system
            confidence, matched_amenities, confidence_factors = calculate_intelligent_score(hotel, query, location_keywords)

            # Skip hotels with zero confidence
            if confidence == 0:
                continue

            # If specific amenities are requested, only include hotels that actually have them
            if amenities_requested and not matched_amenities:
                continue  # Skip hotels that don't have any of the requested amenities

            # Additional city name bonus (only if amenity requirements are met)
            if hotel['city_name'].lower() in query:
                confidence = min(confidence + 0.1, 1.0)
                confidence_factors.append("city_name_mentioned")

            # Boost confidence for exact city matches (only if amenity requirements are met)
            if hotel['city_name'].lower() == query.strip().lower():
                confidence = min(confidence + 0.2, 1.0)
                confidence_factors.append("exact_city_match")

            # Only include high confidence results
            if confidence >= HIGH_CONFIDENCE_THRESHOLD:
                hotel_result = hotel.copy()
                # Remove scoring information for cleaner interface
                hotel_result['matched_amenities'] = matched_amenities
                # Keep confidence_factors for internal use but don't expose scores
                results.append(hotel_result)

        # Sort by hotel name for consistent ordering
        results.sort(key=lambda x: x['name'])

        # Generate clean summary without technical details
        if results:
            summary = f"Found {len(results)} hotels matching your search criteria."
            if results:
                summary += f" Showing results for: {results[0]['city_name']}"
        else:
            summary = "No hotels found matching your criteria."
            if location_keywords:
                available_cities = list(set(h['city_name'] for h in HOTELS_DATA))
                summary += f" Available cities: {', '.join(available_cities)}."

        return jsonify({
            'query': query,
            'result': {
                'results': results,
                'total_results': len(results),
                'summary': summary,
                'ai_powered': False
            },
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return jsonify({
            'error': f'Error processing query: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/api/smart-search', methods=['POST'])
def smart_search():
    """Enhanced smart search"""
    global HOTELS_DATA
    try:
        data = request.get_json()

        cities = data.get('cities', [])
        property_amenities = data.get('property_amenities', [])
        room_amenities = data.get('room_amenities', [])
        room_types = data.get('room_types', [])
        hotel_name = data.get('hotel_name', '')

        results = []
        HIGH_CONFIDENCE_THRESHOLD = Config.HIGH_CONFIDENCE_THRESHOLD  # Use configurable threshold

        for hotel in HOTELS_DATA:
            match_score = 0
            confidence_factors = []
            matched_criteria = []

            # City filter with alias support (high confidence when exact match)
            city_match = True
            if cities:
                # Expand cities to include aliases
                target_cities = []
                city_aliases = get_city_aliases()

                for city in cities:
                    city_lower = city.lower()
                    if city_lower in city_aliases:
                        target_cities.extend(city_aliases[city_lower])
                    else:
                        target_cities.append(city)

                if hotel['city_name'] in target_cities:
                    match_score += 0.3
                    confidence_factors.append("exact_city_match")
                    matched_criteria.append(f"city_{hotel['city_name']}")
                else:
                    city_match = False

            # Hotel name filter (high confidence when partial match)
            name_match = True
            if hotel_name:
                if hotel_name.lower() in hotel['name'].lower():
                    match_score += 0.25
                    confidence_factors.append("name_match")
                    matched_criteria.append("hotel_name")
                else:
                    name_match = False

            # Property amenities filter (medium-high confidence)
            property_amenity_score = 0
            if property_amenities:
                matched_property_amenities = 0
                for amenity in property_amenities:
                    if hotel['property_amenities'].get(amenity, False):
                        matched_property_amenities += 1
                        matched_criteria.append(f"property_{amenity}")

                if matched_property_amenities == len(property_amenities):
                    property_amenity_score = 0.3
                    confidence_factors.append("all_property_amenities_match")
                elif matched_property_amenities > 0:
                    property_amenity_score = 0.2 * (matched_property_amenities / len(property_amenities))
                    confidence_factors.append("partial_property_amenities_match")
                # Removed strict city_match = False to allow partial matches

                match_score += property_amenity_score

            # Room types filter (medium confidence)
            room_type_score = 0
            if room_types:
                available_rooms = list(hotel['room_amenities'].keys())
                matched_room_types = [rt for rt in room_types if rt in available_rooms]

                if matched_room_types:
                    room_type_score = 0.2 * (len(matched_room_types) / len(room_types))
                    confidence_factors.append("room_types_match")
                    matched_criteria.extend([f"room_{rt}" for rt in matched_room_types])
                # Removed strict city_match = False to allow partial matches

                match_score += room_type_score

            # Room amenities filter (medium confidence)
            room_amenity_score = 0
            if room_amenities:
                room_amenity_matches = 0
                for room_type, amenities in hotel['room_amenities'].items():
                    room_matches = sum(1 for amenity in room_amenities if amenities.get(amenity, False))
                    if room_matches == len(room_amenities):
                        room_amenity_matches += 1
                        matched_criteria.append(f"{room_type}_all_amenities")

                if room_amenity_matches > 0:
                    room_amenity_score = 0.2
                    confidence_factors.append("room_amenities_match")
                # Removed strict city_match = False to allow partial matches

                match_score += room_amenity_score

            # Calculate final confidence
            confidence = min(match_score, 1.0)

            # Boost confidence for complete matches
            if len(confidence_factors) >= 2:
                confidence = min(confidence + 0.1, 1.0)

            # Include results that meet confidence threshold and basic criteria
            # Allow results if they match city OR name (not both required) and meet confidence threshold
            basic_criteria_met = (not cities or city_match) and (not hotel_name or name_match)
            if basic_criteria_met and confidence >= HIGH_CONFIDENCE_THRESHOLD:
                hotel_result = hotel.copy()
                hotel_result['relevance_score'] = confidence
                hotel_result['confidence_factors'] = confidence_factors
                hotel_result['matched_criteria'] = matched_criteria
                hotel_result['match_quality'] = 'high' if confidence >= 0.85 else 'medium'
                results.append(hotel_result)

        return jsonify({
            'results': results,
            'count': len(results),
            'filters': data,
            'status': 'success'
        })

    except Exception as e:
        logger.error(f"Error in smart search: {str(e)}")
        return jsonify({
            'error': f'Error in smart search: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/api/hotels', methods=['GET'])
def get_hotels():
    """Get all hotels"""
    global HOTELS_DATA
    try:
        return jsonify({
            'hotels': HOTELS_DATA,
            'count': len(HOTELS_DATA),
            'status': 'success'
        })
    except Exception as e:
        logger.error(f"Error fetching hotels: {str(e)}")
        return jsonify({
            'error': f'Error fetching hotels: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/api/hotels/<int:property_id>', methods=['GET'])
def get_hotel_details(property_id):
    """Get specific hotel details"""
    global HOTELS_DATA
    try:
        hotel = next((h for h in HOTELS_DATA if h['property_id'] == property_id), None)

        if hotel:
            return jsonify({
                'hotel': hotel,
                'status': 'success'
            })
        else:
            return jsonify({
                'error': 'Hotel not found',
                'status': 'error'
            }), 404

    except Exception as e:
        logger.error(f"Error fetching hotel details: {str(e)}")
        return jsonify({
            'error': f'Error fetching hotel details: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/api/cities', methods=['GET'])
def get_cities():
    """Get all cities"""
    global HOTELS_DATA
    try:
        cities = list(set(hotel['city_name'] for hotel in HOTELS_DATA))
        cities.sort()

        return jsonify({
            'cities': cities,
            'count': len(cities),
            'status': 'success'
        })
    except Exception as e:
        logger.error(f"Error fetching cities: {str(e)}")
        return jsonify({
            'error': f'Error fetching cities: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/api/brand-standards', methods=['GET'])
def get_brand_standards():
    """Get all brand standards from database"""
    try:
        import psycopg2
        import psycopg2.extras
        from config import Config

        connection = psycopg2.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )

        cursor = connection.cursor(cursor_factory=psycopg2.extras.DictCursor)

        query = """
        SELECT brand_name, amenity_key, amenity_name, description, category, guaranteed
        FROM brand_standards
        ORDER BY brand_name, category, amenity_key
        """

        cursor.execute(query)
        results = cursor.fetchall()

        cursor.close()
        connection.close()

        # Group by brand
        brands = {}
        for row in results:
            brand = row['brand_name']
            if brand not in brands:
                brands[brand] = []
            brands[brand].append({
                'amenity_key': row['amenity_key'],
                'amenity_name': row['amenity_name'],
                'description': row['description'],
                'category': row['category'],
                'guaranteed': row['guaranteed']
            })

        return jsonify({
            'status': 'success',
            'brands': brands,
            'total_standards': len(results)
        })

    except Exception as e:
        logger.error(f"Error fetching brand standards: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/brand-standards/init', methods=['POST'])
def init_brand_standards():
    """Initialize brand standards table"""
    try:
        success = create_brand_standards_table()
        if success:
            return jsonify({
                'status': 'success',
                'message': 'Brand standards table initialized successfully'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Failed to initialize brand standards table'
            }), 500
    except Exception as e:
        logger.error(f"Error initializing brand standards: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint with AI model information"""
    from config import Config

    # Test database connection
    db_status = 'connected'
    try:
        import psycopg2
        connection = psycopg2.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )
        connection.close()
    except Exception as e:
        db_status = f'error: {str(e)[:50]}...'

    # Get brand standards count
    brand_standards_count = 0
    try:
        brand_standards_count = len(get_all_brand_standard_amenities())
    except:
        pass

    return jsonify({
        'status': 'healthy',
        'service': 'Hotel Portfolio Query Tool',
        'version': '2.0.0',
        'timestamp': datetime.now().isoformat(),
        'ai_intelligence': {
            'enabled': True,
            'model': 'Rule-Based AI + Brand Standards',
            'description': 'Intelligent amenity fallbacks using brand standards and location context',
            'capabilities': [
                'Brand-based amenity assignment (Treebo/Itsy standards)',
                'Location-based amenity inference (hill stations → mountain views)',
                'Name-based amenity detection (Premium hotels → concierge)',
                'Real-time API integration with intelligent fallbacks'
            ],
            'brand_standards_loaded': brand_standards_count > 0,
            'total_brand_standards': brand_standards_count
        },
        'database': {
            'type': 'PostgreSQL',
            'name': Config.DB_NAME,
            'host': Config.DB_HOST,
            'status': db_status,
            'brand_standards_table': 'active',
            'migration_system': 'active'
        },
        'features': {
            'intelligent_amenity_fallbacks': True,
            'brand_standards_database': True,
            'real_time_api_integration': True,
            'location_based_intelligence': True,
            'performance_optimization': True,
            'comprehensive_search': True
        },
        'portfolio': {
            'total_hotels': len(HOTELS_DATA),
            'cities_covered': len(set(hotel['city_name'] for hotel in HOTELS_DATA)),
            'brands': ['Treebo', 'Itsy'],
            'api_integration': 'Treebo Catalog API'
        }
    })

if __name__ == '__main__':
    # Load hotel data on startup
    HOTELS_DATA = load_hotels_from_csv()
    logger.info(f"Application starting with {len(HOTELS_DATA)} hotels")

    # Initialize brand standards table
    initialize_brand_standards()

    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=True)
