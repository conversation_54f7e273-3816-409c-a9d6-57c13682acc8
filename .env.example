# Flask Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
FLASK_ENV=development

# API Configuration
TREEBO_API_BASE_URL=https://catalog.treebo.com/cataloging-service/api/v2/properties/
API_TIMEOUT=10

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=1000

# Database Configuration
DATABASE_URL=postgresql://ashokkumar@localhost/local_hackathon
DB_HOST=localhost
DB_PORT=5432
DB_NAME=local_hackathon
DB_USER=ashokkumar
DB_PASSWORD=

# Data Configuration
HOTEL_METADATA_FILE=hotel_metadata.csv

# Cache Configuration
ENABLE_CACHE=True
CACHE_TIMEOUT=3600

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=hotel_query_tool.log

# NLP Configuration
SIMILARITY_THRESHOLD=0.6
FUZZY_MATCH_THRESHOLD=60

# Query Processing Configuration
MAX_RESULTS=50
DEFAULT_RESULTS_LIMIT=20

# Rate Limiting Configuration
RATE_LIMIT_ENABLED=False
RATE_LIMIT_PER_MINUTE=60

# Server Configuration
PORT=5000
