#!/usr/bin/env python3
"""
Local server runner for sharing the hotel search tool on local network
"""

import os
import socket
import subprocess
import sys
from config import Config

def get_local_ip():
    """Get the local IP address"""
    try:
        # Connect to a remote server to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except Exception:
        return "127.0.0.1"

def main():
    """Run the server with local network access"""
    
    # Get local IP
    local_ip = get_local_ip()
    port = int(os.environ.get('PORT', 5000))
    
    print("=" * 60)
    print("🏨 Hotel Portfolio Query Tool - Local Network Server")
    print("=" * 60)
    print(f"🌐 Local Access: http://localhost:{port}")
    print(f"🌐 Network Access: http://{local_ip}:{port}")
    print("=" * 60)
    print("📱 Share this URL with your colleague:")
    print(f"   http://{local_ip}:{port}")
    print("=" * 60)
    print("🔧 Available Endpoints:")
    print(f"   • Main Interface: http://{local_ip}:{port}/")
    print(f"   • Dashboard: http://{local_ip}:{port}/dashboard")
    print(f"   • Hotel Details: http://{local_ip}:{port}/hotel-details")
    print(f"   • API Health: http://{local_ip}:{port}/health")
    print("=" * 60)
    print("💡 Make sure your colleague is on the same network!")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)
    
    # Choose which app to run
    app_choice = input("\nWhich app would you like to run?\n1. Simple App (demo with sample data)\n2. Full App (with database)\nEnter choice (1 or 2): ").strip()
    
    if app_choice == "1":
        app_file = "simple_app.py"
        print(f"\n🚀 Starting Simple App on {local_ip}:{port}...")
    else:
        app_file = "app.py"
        print(f"\n🚀 Starting Full App on {local_ip}:{port}...")
    
    try:
        # Run the Flask app
        env = os.environ.copy()
        env['FLASK_ENV'] = 'development'
        env['DEBUG'] = 'True'
        
        subprocess.run([
            sys.executable, app_file
        ], env=env)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()
