"""
Enhanced Hotel Metadata Service for intelligent hotel filtering and decision making
"""

import pandas as pd
import logging
from typing import Dict, List, Optional, Set, Any
from fuzzywuzzy import fuzz, process
import re

logger = logging.getLogger(__name__)

class HotelMetadataService:
    def __init__(self, csv_path: str = 'hotel_metadata.csv'):
        self.csv_path = csv_path
        self.hotels_df = None
        self.hotels_by_city = {}
        self.hotels_by_state = {}
        self.all_cities = set()
        self.all_states = set()
        self.all_localities = set()
        self.load_hotel_metadata()
    
    def load_hotel_metadata(self):
        """Load hotel metadata from CSV file"""
        try:
            self.hotels_df = pd.read_csv(self.csv_path)
            logger.info(f"Loading hotel metadata from {self.csv_path}")
            
            # Clean and process data
            self.hotels_df = self.hotels_df[self.hotels_df['status'] == 'LIVE'].copy()
            self.hotels_df['city_name'] = self.hotels_df['city_name'].fillna('').str.strip()
            self.hotels_df['locality_name'] = self.hotels_df['locality_name'].fillna('').str.strip()
            self.hotels_df['name'] = self.hotels_df['name'].fillna('').str.strip()
            
            # Build indexes
            self._build_indexes()
            
            logger.info(f"Loaded {len(self.hotels_df)} live hotels from {len(self.all_cities)} cities")
            
        except Exception as e:
            logger.error(f"Error loading hotel metadata: {str(e)}")
            self.hotels_df = pd.DataFrame()
    
    def _build_indexes(self):
        """Build various indexes for fast lookup"""
        if self.hotels_df is None or self.hotels_df.empty:
            return
        
        # Group by city
        for city, group in self.hotels_df.groupby('city_name'):
            if city:  # Skip empty city names
                self.hotels_by_city[city] = group.to_dict('records')
                self.all_cities.add(city)
        
        # Group by state (if state column exists)
        if 'state_name' in self.hotels_df.columns:
            for state, group in self.hotels_df.groupby('state_name'):
                if pd.notna(state) and state:
                    self.hotels_by_state[state] = group.to_dict('records')
                    self.all_states.add(state)
        
        # Collect all localities
        self.all_localities = set(self.hotels_df['locality_name'].dropna().unique())
    
    def get_hotels_by_city(self, city: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get all hotels in a specific city"""
        hotels = self.hotels_by_city.get(city, [])
        if limit:
            hotels = hotels[:limit]
        return hotels
    
    def get_hotels_by_state(self, state: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get all hotels in a specific state"""
        hotels = self.hotels_by_state.get(state, [])
        if limit:
            hotels = hotels[:limit]
        return hotels
    
    def search_hotels_by_name(self, name_query: str, threshold: int = 60) -> List[Dict[str, Any]]:
        """Search hotels by name using fuzzy matching"""
        if not name_query or self.hotels_df is None:
            return []
        
        # Extract hotel names
        hotel_names = self.hotels_df['name'].tolist()
        
        # Fuzzy match
        matches = process.extract(name_query, hotel_names, limit=10)
        
        results = []
        for match_name, score in matches:
            if score >= threshold:
                # Find the hotel record
                hotel_record = self.hotels_df[self.hotels_df['name'] == match_name].iloc[0].to_dict()
                hotel_record['name_match_score'] = score
                results.append(hotel_record)
        
        return results
    
    def search_hotels_by_locality(self, locality_query: str, city: Optional[str] = None) -> List[Dict[str, Any]]:
        """Search hotels by locality"""
        if not locality_query or self.hotels_df is None:
            return []
        
        df = self.hotels_df
        if city:
            df = df[df['city_name'] == city]
        
        # Fuzzy match localities
        localities = df['locality_name'].dropna().unique().tolist()
        matches = process.extract(locality_query, localities, limit=5)
        
        results = []
        for match_locality, score in matches:
            if score >= 70:  # Higher threshold for localities
                locality_hotels = df[df['locality_name'] == match_locality].to_dict('records')
                for hotel in locality_hotels:
                    hotel['locality_match_score'] = score
                    results.append(hotel)
        
        return results
    
    def get_hotels_near_location(self, location_query: str, city: Optional[str] = None) -> List[Dict[str, Any]]:
        """Find hotels near a specific location (like airport, railway station, etc.)"""
        if not location_query or self.hotels_df is None:
            return []
        
        df = self.hotels_df
        if city:
            df = df[df['city_name'] == city]
        
        # Search in postal addresses and locality names
        location_lower = location_query.lower()
        location_keywords = ['airport', 'station', 'railway', 'bus stand', 'mall', 'hospital', 'university']
        
        results = []
        
        # Check if query contains location keywords
        is_location_query = any(keyword in location_lower for keyword in location_keywords)
        
        if is_location_query:
            # Search in postal addresses
            for _, hotel in df.iterrows():
                address = str(hotel.get('postal_address', '')).lower()
                locality = str(hotel.get('locality_name', '')).lower()
                
                # Calculate relevance score
                address_score = fuzz.partial_ratio(location_lower, address)
                locality_score = fuzz.partial_ratio(location_lower, locality)
                max_score = max(address_score, locality_score)
                
                if max_score >= 60:
                    hotel_dict = hotel.to_dict()
                    hotel_dict['location_match_score'] = max_score
                    results.append(hotel_dict)
        
        # Sort by relevance score
        results.sort(key=lambda x: x.get('location_match_score', 0), reverse=True)
        return results[:20]
    
    def get_advanced_hotel_suggestions(self, query: str, city: Optional[str] = None) -> Dict[str, Any]:
        """Get comprehensive hotel suggestions based on query"""
        suggestions = {
            'by_name': [],
            'by_locality': [],
            'by_location': [],
            'by_city': [],
            'total_suggestions': 0
        }
        
        if not query:
            return suggestions
        
        # Search by hotel name
        name_results = self.search_hotels_by_name(query, threshold=50)
        suggestions['by_name'] = name_results[:5]
        
        # Search by locality
        locality_results = self.search_hotels_by_locality(query, city)
        suggestions['by_locality'] = locality_results[:5]
        
        # Search by location
        location_results = self.get_hotels_near_location(query, city)
        suggestions['by_location'] = location_results[:5]
        
        # If no city specified, search for city matches
        if not city:
            city_matches = []
            for city_name in self.all_cities:
                if fuzz.partial_ratio(query.lower(), city_name.lower()) >= 70:
                    city_hotels = self.get_hotels_by_city(city_name, limit=3)
                    city_matches.extend(city_hotels)
            suggestions['by_city'] = city_matches[:5]
        
        suggestions['total_suggestions'] = (
            len(suggestions['by_name']) + 
            len(suggestions['by_locality']) + 
            len(suggestions['by_location']) + 
            len(suggestions['by_city'])
        )
        
        return suggestions
    
    def get_hotel_by_id(self, property_id: int) -> Optional[Dict[str, Any]]:
        """Get hotel by property ID"""
        if self.hotels_df is None:
            return None
        
        hotel_row = self.hotels_df[self.hotels_df['property_id'] == property_id]
        if not hotel_row.empty:
            return hotel_row.iloc[0].to_dict()
        return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get statistics about the hotel metadata"""
        if self.hotels_df is None:
            return {}
        
        return {
            'total_hotels': len(self.hotels_df),
            'total_cities': len(self.all_cities),
            'total_states': len(self.all_states),
            'total_localities': len(self.all_localities),
            'hotels_by_city': {city: len(hotels) for city, hotels in self.hotels_by_city.items()},
            'top_cities': sorted(
                [(city, len(hotels)) for city, hotels in self.hotels_by_city.items()],
                key=lambda x: x[1], reverse=True
            )[:10]
        }
