"""
Amenity Verification Service for intelligent amenity matching with real-time API data
"""

import logging
from typing import Dict, List, Any, Optional, Set
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import time

logger = logging.getLogger(__name__)

class AmenityVerificationService:
    def __init__(self, hotel_service, brand_standards_service=None, geographic_intelligence_service=None):
        self.hotel_service = hotel_service
        self.brand_standards_service = brand_standards_service
        self.geographic_intelligence_service = geographic_intelligence_service
        self.verification_cache = {}
        self.cache_ttl = 3600  # 1 hour cache

        # Comprehensive amenity mapping for intelligent matching
        self.amenity_mappings = {
            'gym': {
                'api_keys': ['gym', 'fitness', 'fitness_center', 'exercise', 'workout'],
                'keywords': ['gym', 'fitness', 'fitness center', 'exercise', 'workout', 'gymnasium'],
                'category': 'recreation'
            },
            'spa': {
                'api_keys': ['spa', 'massage', 'wellness', 'ayurveda', 'spa_services'],
                'keywords': ['spa', 'massage', 'wellness', 'ayurveda', 'spa services', 'therapeutic'],
                'category': 'wellness'
            },
            'swimming_pool': {
                'api_keys': ['swimming_pool', 'pool', 'swimming'],
                'keywords': ['swimming pool', 'pool', 'swimming', 'swim'],
                'category': 'recreation'
            },
            'restaurant': {
                'api_keys': ['restaurant', 'dining', 'food', 'multi_cuisine'],
                'keywords': ['restaurant', 'dining', 'food', 'multi cuisine', 'in-house restaurant'],
                'category': 'dining'
            },
            'parking': {
                'api_keys': ['parking', 'valet_parking', 'car_park'],
                'keywords': ['parking', 'valet parking', 'car park', 'vehicle parking'],
                'category': 'convenience'
            },
            'banquet': {
                'api_keys': ['banquet', 'banquet_hall', 'conference', 'meeting_room', 'event_hall'],
                'keywords': ['banquet', 'banquet hall', 'conference', 'meeting room', 'event hall', 'function hall'],
                'category': 'business'
            },
            'elevator': {
                'api_keys': ['elevator', 'lift'],
                'keywords': ['elevator', 'lift'],
                'category': 'convenience'
            },
            'laundry': {
                'api_keys': ['laundry', 'guest_laundry', 'dry_cleaning'],
                'keywords': ['laundry', 'guest laundry', 'dry cleaning', 'washing'],
                'category': 'service'
            }
        }

    def verify_hotel_amenities(self, property_id: int, required_amenities: List[str]) -> Dict[str, Any]:
        """
        Verify if a hotel has the required amenities using real-time API data
        """
        cache_key = f"{property_id}_{hash(tuple(sorted(required_amenities)))}"

        # Check cache first
        if cache_key in self.verification_cache:
            cached_result = self.verification_cache[cache_key]
            if time.time() - cached_result['timestamp'] < self.cache_ttl:
                logger.debug(f"Using cached amenity verification for hotel {property_id}")
                return cached_result['data']

        try:
            # Get real-time hotel details from API
            hotel_details = self.hotel_service.get_hotel_details(property_id)

            if not hotel_details:
                logger.warning(f"No hotel details found for property {property_id}")
                return self._create_verification_result(property_id, required_amenities, {}, {}, False)

            # Extract amenities from API response
            property_amenities = hotel_details.get('property_amenities', {})
            room_amenities = hotel_details.get('room_amenities', {})
            amenity_summary = hotel_details.get('amenity_summary', {})

            # Perform intelligent amenity matching
            verification_result = self._intelligent_amenity_matching(
                property_id, required_amenities, property_amenities, room_amenities, amenity_summary
            )

            # Cache the result
            self.verification_cache[cache_key] = {
                'data': verification_result,
                'timestamp': time.time()
            }

            return verification_result

        except Exception as e:
            logger.error(f"Error verifying amenities for hotel {property_id}: {str(e)}")
            return self._create_verification_result(property_id, required_amenities, {}, {}, False)

    def _intelligent_amenity_matching(self, property_id: int, required_amenities: List[str],
                                    property_amenities: Dict[str, Any], room_amenities: Dict[str, Any],
                                    amenity_summary: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform intelligent amenity matching using multiple data sources
        """
        matched_amenities = []
        missing_amenities = []
        amenity_details = {}

        for required_amenity in required_amenities:
            amenity_found = False
            amenity_source = None
            amenity_location = None

            # Check if it's a brand standard amenity
            if self._is_brand_standard_amenity(required_amenity):
                amenity_found = True
                amenity_source = 'brand_standards'
                amenity_location = 'property'
                logger.info(f"✅ {required_amenity} confirmed as Treebo brand standard for hotel {property_id}")

            # CRITICAL: Check if it's an implicit geographic amenity
            elif self._is_implicit_geographic_amenity(property_id, required_amenity):
                amenity_found = True
                amenity_source = 'geographic_implicit'
                amenity_location = 'natural'
                logger.info(f"🏔️ {required_amenity} confirmed as IMPLICIT GEOGRAPHIC amenity for hotel {property_id}")

            # Check in property amenities
            elif self._check_amenity_in_property(required_amenity, property_amenities):
                amenity_found = True
                amenity_source = 'property_amenities'
                amenity_location = 'property'
                logger.info(f"✅ {required_amenity} found in property amenities for hotel {property_id}")

            # Check in room amenities
            elif self._check_amenity_in_rooms(required_amenity, room_amenities):
                amenity_found = True
                amenity_source = 'room_amenities'
                amenity_location = 'room'
                logger.info(f"✅ {required_amenity} found in room amenities for hotel {property_id}")

            # Check in amenity summary structure
            elif self._check_amenity_in_summary(required_amenity, amenity_summary):
                amenity_found = True
                amenity_source = 'amenity_summary'
                amenity_location = 'property'
                logger.info(f"✅ {required_amenity} found in amenity summary for hotel {property_id}")

            if amenity_found:
                matched_amenities.append(required_amenity)
                amenity_details[required_amenity] = {
                    'source': amenity_source,
                    'location': amenity_location,
                    'verified': True
                }
            else:
                missing_amenities.append(required_amenity)
                logger.warning(f"❌ {required_amenity} NOT found for hotel {property_id}")

        # Hotel passes verification only if ALL required amenities are found
        all_amenities_verified = len(missing_amenities) == 0

        return self._create_verification_result(
            property_id, required_amenities, matched_amenities, missing_amenities,
            all_amenities_verified, amenity_details
        )

    def _is_brand_standard_amenity(self, amenity: str) -> bool:
        """Check if amenity is a guaranteed brand standard"""
        brand_standards = ['breakfast', 'wifi', 'geyser', 'security']
        amenity_lower = amenity.lower()

        for standard in brand_standards:
            if standard in amenity_lower or amenity_lower in standard:
                return True

        if self.brand_standards_service:
            return self.brand_standards_service.is_guaranteed_amenity(amenity_lower, 'treebo')

        return False

    def _is_implicit_geographic_amenity(self, property_id: int, amenity: str) -> bool:
        """
        Check if amenity is implicitly available due to hotel's geographic location
        CRITICAL for mountain view in mountain cities, beach view in coastal cities
        """
        if not self.geographic_intelligence_service:
            return False

        # Get hotel details to find city
        try:
            hotel_details = self.hotel_service.get_hotel_details(property_id)
            if not hotel_details:
                return False

            # Extract city from hotel details
            city = hotel_details.get('city_name', '')
            if not city:
                return False

            # Check if this amenity is naturally available in this city
            amenity_lower = amenity.lower()

            # Map amenity keywords to view types
            view_mappings = {
                'mountain': ['mountain', 'hill', 'valley', 'peak'],
                'beach': ['beach', 'sea', 'ocean', 'coastal'],
                'city': ['city', 'urban', 'skyline']
            }

            for view_type, keywords in view_mappings.items():
                if any(keyword in amenity_lower for keyword in keywords):
                    if self.geographic_intelligence_service.does_city_provide_view_naturally(city, view_type):
                        logger.info(f"🌍 GEOGRAPHIC INTELLIGENCE: {amenity} naturally available in {city}")
                        return True

            return False

        except Exception as e:
            logger.error(f"Error checking implicit geographic amenity: {str(e)}")
            return False

    def _check_amenity_in_property(self, amenity: str, property_amenities: Dict[str, Any]) -> bool:
        """Check if amenity exists in property amenities"""
        if not property_amenities:
            return False

        amenity_mapping = self.amenity_mappings.get(amenity, {})
        api_keys = amenity_mapping.get('api_keys', [amenity])
        keywords = amenity_mapping.get('keywords', [amenity])

        # Check direct key matches
        for api_key in api_keys:
            if api_key in property_amenities:
                return True

        # Check keyword matches in amenity names/descriptions
        property_text = str(property_amenities).lower()
        for keyword in keywords:
            if keyword.lower() in property_text:
                return True

        return False

    def _check_amenity_in_rooms(self, amenity: str, room_amenities: Dict[str, Any]) -> bool:
        """Check if amenity exists in any room type"""
        if not room_amenities:
            return False

        amenity_mapping = self.amenity_mappings.get(amenity, {})
        api_keys = amenity_mapping.get('api_keys', [amenity])
        keywords = amenity_mapping.get('keywords', [amenity])

        # Check all room types
        for room_type, amenities in room_amenities.items():
            if isinstance(amenities, dict):
                # Check direct key matches
                for api_key in api_keys:
                    if api_key in amenities:
                        return True

                # Check keyword matches
                room_text = str(amenities).lower()
                for keyword in keywords:
                    if keyword.lower() in room_text:
                        return True

        return False

    def _check_amenity_in_summary(self, amenity: str, amenity_summary: Dict[str, Any]) -> bool:
        """Check if amenity exists in amenity summary structure"""
        if not amenity_summary:
            return False

        amenity_mapping = self.amenity_mappings.get(amenity, {})
        api_keys = amenity_mapping.get('api_keys', [amenity])
        keywords = amenity_mapping.get('keywords', [amenity])

        # Check in property amenities list
        property_amenities = amenity_summary.get('property_amenities', [])
        for amenity_item in property_amenities:
            if isinstance(amenity_item, dict):
                amenity_key = amenity_item.get('amenity_key', '').lower()
                amenity_name = amenity_item.get('amenity_name', '').lower()

                # Check against API keys and keywords
                for api_key in api_keys:
                    if api_key.lower() in amenity_key or api_key.lower() in amenity_name:
                        return True

                for keyword in keywords:
                    if keyword.lower() in amenity_key or keyword.lower() in amenity_name:
                        return True

        # Check in room amenities for each room type
        room_types = ['ACACIA', 'MAPLE', 'OAK', 'MAHOGANY']
        for room_type in room_types:
            room_amenities = amenity_summary.get(room_type, [])
            for amenity_item in room_amenities:
                if isinstance(amenity_item, dict):
                    amenity_key = amenity_item.get('amenity_key', '').lower()
                    amenity_name = amenity_item.get('amenity_name', '').lower()

                    for api_key in api_keys:
                        if api_key.lower() in amenity_key or api_key.lower() in amenity_name:
                            return True

                    for keyword in keywords:
                        if keyword.lower() in amenity_key or keyword.lower() in amenity_name:
                            return True

        return False

    def _create_verification_result(self, property_id: int, required_amenities: List[str],
                                  matched_amenities: List[str], missing_amenities: List[str],
                                  verified: bool, amenity_details: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create standardized verification result"""
        return {
            'property_id': property_id,
            'required_amenities': required_amenities,
            'matched_amenities': matched_amenities,
            'missing_amenities': missing_amenities,
            'all_amenities_verified': verified,
            'verification_score': len(matched_amenities) / len(required_amenities) if required_amenities else 0,
            'amenity_details': amenity_details or {},
            'timestamp': time.time()
        }

    def batch_verify_hotels(self, hotel_property_ids: List[int], required_amenities: List[str]) -> Dict[int, Dict[str, Any]]:
        """
        Verify amenities for multiple hotels efficiently
        """
        results = {}

        # Use ThreadPoolExecutor for parallel verification
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_property = {
                executor.submit(self.verify_hotel_amenities, property_id, required_amenities): property_id
                for property_id in hotel_property_ids
            }

            for future in future_to_property:
                property_id = future_to_property[future]
                try:
                    verification_result = future.result(timeout=30)  # 30 second timeout per hotel
                    results[property_id] = verification_result
                except Exception as e:
                    logger.error(f"Error verifying amenities for hotel {property_id}: {str(e)}")
                    results[property_id] = self._create_verification_result(
                        property_id, required_amenities, [], required_amenities, False
                    )

        return results

    def clear_cache(self):
        """Clear the verification cache"""
        self.verification_cache.clear()
        logger.info("Amenity verification cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self.verification_cache),
            'cache_ttl': self.cache_ttl,
            'supported_amenities': list(self.amenity_mappings.keys())
        }
