"""
City Alias Service for enhanced city matching using city_alias.csv
"""

import pandas as pd
import logging
from typing import Dict, List, Optional, Set
from fuzzywuzzy import fuzz, process

logger = logging.getLogger(__name__)

class CityAliasService:
    def __init__(self, csv_path: str = 'city_alias.csv'):
        self.csv_path = csv_path
        self.city_aliases = {}
        self.reverse_aliases = {}
        self.all_cities = set()
        self.all_aliases = set()
        self.load_city_aliases()

    def load_city_aliases(self):
        """Load city aliases from CSV file"""
        try:
            df = pd.read_csv(self.csv_path)
            logger.info(f"Loading city aliases from {self.csv_path}")

            # Build alias mappings
            for _, row in df.iterrows():
                alias = row['city_alias_name'].strip()
                city = row['city_name'].strip()

                # Store alias -> city mapping
                self.city_aliases[alias.lower()] = city

                # Store city -> aliases mapping
                if city not in self.reverse_aliases:
                    self.reverse_aliases[city] = []
                self.reverse_aliases[city].append(alias)

                # Track all cities and aliases
                self.all_cities.add(city)
                self.all_aliases.add(alias.lower())

            logger.info(f"Loaded {len(self.city_aliases)} city aliases for {len(self.all_cities)} cities")

        except Exception as e:
            logger.error(f"Error loading city aliases: {str(e)}")
            self.city_aliases = {}
            self.reverse_aliases = {}

    def resolve_city_name(self, query_city: str) -> Optional[str]:
        """Resolve a city name or alias to the canonical city name"""
        if not query_city:
            return None

        query_lower = query_city.lower().strip()

        # Direct alias match
        if query_lower in self.city_aliases:
            resolved = self.city_aliases[query_lower]
            logger.info(f"Resolved city alias '{query_city}' -> '{resolved}'")
            return resolved

        # Direct city name match (case insensitive)
        for city in self.all_cities:
            if city.lower() == query_lower:
                return city

        return None

    def resolve_state_to_cities(self, query_state: str) -> List[str]:
        """Resolve a state name to all cities in that state"""
        if not query_state:
            return []

        query_lower = query_state.lower().strip()

        # State to cities mapping - CRITICAL for Himachal Pradesh
        state_mappings = {
            'himachal': ['Shimla', 'Manali', 'Dharamshala'],
            'himachal pradesh': ['Shimla', 'Manali', 'Dharamshala'],
            'hp': ['Shimla', 'Manali', 'Dharamshala'],
            'goa': ['Goa'],
            'kerala': ['Kochi', 'Thiruvananthapuram'],
            'rajasthan': ['Jaipur', 'Udaipur', 'Jodhpur'],
            'maharashtra': ['Mumbai', 'Pune'],
            'karnataka': ['Bangalore', 'Mysore'],
            'tamil nadu': ['Chennai'],
            'west bengal': ['Kolkata'],
            'delhi': ['Delhi', 'New Delhi'],
            'uttar pradesh': ['Lucknow', 'Agra'],
            'gujarat': ['Ahmedabad']
        }

        # Check for direct state match
        if query_lower in state_mappings:
            cities = state_mappings[query_lower]
            # Filter to only cities that exist in our database
            available_cities = [city for city in cities if city in self.all_cities]
            if available_cities:
                logger.warning(f"🏔️ CRITICAL: Resolved state '{query_state}' to cities: {available_cities}")
                return available_cities

        return []

    def resolve_city_name_enhanced(self, query_city: str) -> List[str]:
        """
        Enhanced city resolution that handles both single cities and states
        CRITICAL for queries like 'himachal' which should return multiple cities
        """
        if not query_city:
            return []

        # First try state resolution
        state_cities = self.resolve_state_to_cities(query_city)
        if state_cities:
            return state_cities

        # Then try single city resolution
        single_city = self.resolve_city_name(query_city)
        if single_city:
            return [single_city]

        return []

    def find_similar_cities(self, query_city: str, threshold: int = 70) -> List[Dict[str, any]]:
        """Find similar cities using fuzzy matching"""
        if not query_city:
            return []

        query_lower = query_city.lower().strip()
        matches = []

        # Search in city names
        city_matches = process.extract(query_lower, [city.lower() for city in self.all_cities], limit=5)
        for match, score in city_matches:
            if score >= threshold:
                # Find the original city name
                original_city = next(city for city in self.all_cities if city.lower() == match)
                matches.append({
                    'city': original_city,
                    'similarity': score,
                    'match_type': 'city_name',
                    'query': query_city
                })

        # Search in aliases
        alias_matches = process.extract(query_lower, list(self.all_aliases), limit=5)
        for match, score in alias_matches:
            if score >= threshold:
                resolved_city = self.city_aliases[match]
                # Avoid duplicates
                if not any(m['city'] == resolved_city for m in matches):
                    matches.append({
                        'city': resolved_city,
                        'similarity': score,
                        'match_type': 'alias',
                        'matched_alias': match,
                        'query': query_city
                    })

        # Sort by similarity score
        matches.sort(key=lambda x: x['similarity'], reverse=True)
        return matches[:5]

    def get_all_aliases_for_city(self, city: str) -> List[str]:
        """Get all aliases for a given city"""
        return self.reverse_aliases.get(city, [])

    def get_city_suggestions(self, partial_query: str, limit: int = 10) -> List[Dict[str, any]]:
        """Get city suggestions for autocomplete"""
        if not partial_query or len(partial_query) < 2:
            return []

        suggestions = []
        query_lower = partial_query.lower()

        # Find cities that start with the query
        for city in self.all_cities:
            if city.lower().startswith(query_lower):
                suggestions.append({
                    'city': city,
                    'type': 'city',
                    'match_score': 100
                })

        # Find aliases that start with the query
        for alias, city in self.city_aliases.items():
            if alias.startswith(query_lower):
                suggestions.append({
                    'city': city,
                    'type': 'alias',
                    'alias': alias,
                    'match_score': 95
                })

        # Sort by match score and remove duplicates
        seen_cities = set()
        unique_suggestions = []
        for suggestion in sorted(suggestions, key=lambda x: x['match_score'], reverse=True):
            if suggestion['city'] not in seen_cities:
                unique_suggestions.append(suggestion)
                seen_cities.add(suggestion['city'])

        return unique_suggestions[:limit]

    def get_statistics(self) -> Dict[str, any]:
        """Get statistics about the city alias data"""
        return {
            'total_cities': len(self.all_cities),
            'total_aliases': len(self.city_aliases),
            'cities_with_aliases': len(self.reverse_aliases),
            'avg_aliases_per_city': len(self.city_aliases) / len(self.all_cities) if self.all_cities else 0
        }
