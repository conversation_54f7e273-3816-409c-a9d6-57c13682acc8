import logging
import pandas as pd
from typing import Dict, List, Any, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
from sqlalchemy import create_engine, text
from config import Config
import json

logger = logging.getLogger(__name__)

class DatabaseService:
    """
    Database service for PostgreSQL integration
    """

    def __init__(self):
        self.connection_string = Config.DATABASE_URL
        self.engine = None
        self.connection = None
        self._initialize_connection()

    def _initialize_connection(self):
        """Initialize database connection"""
        try:
            # SQLAlchemy engine for pandas operations
            self.engine = create_engine(self.connection_string)

            # Direct psycopg2 connection for custom queries
            self.connection = psycopg2.connect(
                host=Config.DB_HOST,
                port=Config.DB_PORT,
                database=Config.DB_NAME,
                user=Config.DB_USER,
                password=Config.DB_PASSWORD
            )

            # Set autocommit to avoid transaction issues
            self.connection.autocommit = True

            logger.info("Database connection established successfully")

        except Exception as e:
            logger.error(f"Failed to connect to database: {str(e)}")
            self.engine = None
            self.connection = None

    def _ensure_connection(self):
        """Ensure database connection is healthy"""
        try:
            if not self.connection or self.connection.closed:
                logger.info("Reconnecting to database...")
                self._initialize_connection()
            else:
                # Test the connection
                cursor = self.connection.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
        except Exception as e:
            logger.error(f"Connection test failed, reconnecting: {str(e)}")
            try:
                if self.connection:
                    self.connection.close()
            except:
                pass
            self._initialize_connection()

    def get_hotel_count(self) -> int:
        """Get total number of hotels"""
        try:
            query = """
            SELECT COUNT(*) as total
            FROM hotel_metadata
            WHERE status = 'LIVE'
            """

            with self.connection.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                return result[0] if result else 0

        except Exception as e:
            logger.error(f"Error getting hotel count: {str(e)}")
            return 0

    def get_all_cities(self) -> List[str]:
        """Get all unique cities"""
        try:
            query = """
            SELECT DISTINCT city_name
            FROM hotel_metadata
            WHERE status = 'LIVE' AND city_name IS NOT NULL
            ORDER BY city_name
            """

            with self.connection.cursor() as cursor:
                cursor.execute(query)
                results = cursor.fetchall()
                return [row[0] for row in results]

        except Exception as e:
            logger.error(f"Error getting cities: {str(e)}")
            return []

    def get_sample_hotels(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get sample hotels for context"""
        try:
            query = """
            SELECT property_id, name, city_name, locality_name
            FROM hotel_metadata
            WHERE status = 'LIVE'
            ORDER BY name
            LIMIT %s
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (limit,))
                results = cursor.fetchall()
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error getting sample hotels: {str(e)}")
            return []



    def get_available_amenities(self) -> List[str]:
        """Get available amenities from database or predefined list"""
        # Since amenities come from API, return predefined list
        return [
            'swimming_pool', 'spa', 'banquet', 'parking', 'breakfast',
            'elevator', 'laundry', 'wifi', 'gym', 'restaurant', 'room_service',
            'ac', 'tv', 'balcony', 'view'
        ]

    def get_available_room_types(self) -> List[str]:
        """Get available room types"""
        return [
            'twin_bed', 'double_bed', 'single_bed', 'king_bed', 'queen_bed',
            'deluxe_room', 'standard_room', 'suite'
        ]

    def search_hotels(self, **kwargs) -> List[Dict[str, Any]]:
        """
        Advanced hotel search with multiple filters and amenity support
        """
        try:
            # Base query
            base_query = """
            SELECT
                property_id,
                name,
                city_name,
                locality_name,
                micro_market_name,
                latitude,
                longitude,
                postal_address,
                maps_link,
                status
            FROM hotel_metadata
            WHERE status = 'LIVE'
            """

            conditions = []
            params = []

            # City filter with alias support
            cities = kwargs.get('cities') or kwargs.get('city')
            if cities:
                if isinstance(cities, str):
                    cities = [cities]

                # Expand cities to include aliases
                expanded_cities = []
                city_aliases = self._get_city_aliases()

                for city in cities:
                    city_lower = city.lower()
                    if city_lower in city_aliases:
                        expanded_cities.extend(city_aliases[city_lower])
                    else:
                        expanded_cities.append(city)

                # Remove duplicates and create placeholders
                expanded_cities = list(set(expanded_cities))
                city_placeholders = ','.join(['%s'] * len(expanded_cities))
                conditions.append(f"LOWER(city_name) IN ({city_placeholders})")
                params.extend([city.lower() for city in expanded_cities])

            # State filter (if you have state data)
            states = kwargs.get('states') or kwargs.get('state')
            if states:
                if isinstance(states, str):
                    states = [states]
                state_placeholders = ','.join(['%s'] * len(states))
                conditions.append(f"LOWER(state_name) IN ({state_placeholders})")
                params.extend([state.lower() for state in states])

            # Locality filter
            localities = kwargs.get('localities') or kwargs.get('locality')
            if localities:
                if isinstance(localities, str):
                    localities = [localities]
                locality_placeholders = ','.join(['%s'] * len(localities))
                conditions.append(f"LOWER(locality_name) IN ({locality_placeholders})")
                params.extend([locality.lower() for locality in localities])

            # Hotel name filter
            hotel_names = kwargs.get('hotel_names') or kwargs.get('hotel_name') or kwargs.get('property_name')
            if hotel_names:
                if isinstance(hotel_names, str):
                    hotel_names = [hotel_names]
                name_conditions = []
                for name in hotel_names:
                    name_conditions.append("LOWER(name) LIKE %s")
                    params.append(f"%{name.lower()}%")
                conditions.append(f"({' OR '.join(name_conditions)})")

            # Add conditions to query
            if conditions:
                base_query += " AND " + " AND ".join(conditions)

            # Add ordering
            base_query += " ORDER BY name"

            # Add limit - but check if we're dealing with brand standard amenities
            required_amenities = kwargs.get('required_amenities') or kwargs.get('amenities')
            brand_standard_amenities = ['breakfast', 'wifi', 'geyser', 'security']

            # If query involves brand standards, don't limit results (return full portfolio)
            if required_amenities and any(amenity.lower() in brand_standard_amenities for amenity in required_amenities):
                logger.info(f"Brand standard amenities detected: {required_amenities}. Returning full portfolio.")
                # No limit for brand standards
            else:
                limit = kwargs.get('limit', 50)
                base_query += f" LIMIT {limit}"

            # Execute query
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(base_query, params)
                initial_results = cursor.fetchall()
                results = [dict(row) for row in initial_results]

            # Handle amenity filtering with API calls
            required_amenities = kwargs.get('required_amenities') or kwargs.get('amenities')
            if required_amenities and results:
                logger.info(f"Filtering {len(results)} hotels for amenities: {required_amenities}")
                filtered_results = self._filter_hotels_by_amenities(results, required_amenities)
                logger.info(f"After amenity filtering: {len(filtered_results)} hotels")
                return filtered_results

            logger.info(f"Search returned {len(results)} hotels")
            return results

        except Exception as e:
            logger.error(f"Error searching hotels: {str(e)}")
            return []

    def _filter_hotels_by_amenities(self, hotels: List[Dict[str, Any]], required_amenities: List[str]) -> List[Dict[str, Any]]:
        """
        Filter hotels by amenities using API calls
        """
        filtered_hotels = []

        # Import here to avoid circular imports
        from services.hotel_service import HotelService

        # Create hotel service instance for API calls
        hotel_service = HotelService(self)

        for hotel in hotels:
            try:
                property_id = hotel['property_id']

                # Get detailed amenity data from API
                hotel_details = hotel_service.get_hotel_details(property_id)

                if hotel_details and self._hotel_has_required_amenities(hotel_details, required_amenities):
                    # Add amenity information to hotel data
                    hotel['matched_amenities'] = required_amenities
                    hotel['api_data_available'] = True
                    hotel['amenity_details'] = hotel_details.get('property_amenities', {})
                    filtered_hotels.append(hotel)

            except Exception as e:
                logger.error(f"Error checking amenities for hotel {hotel.get('property_id')}: {str(e)}")
                # Continue with next hotel
                continue

        return filtered_hotels

    def _hotel_has_required_amenities(self, hotel_details: Dict[str, Any], required_amenities: List[str]) -> bool:
        """
        Check if hotel has all required amenities using the correct API structure
        """
        try:
            # Get the raw API data which contains amenity_summary
            api_data = hotel_details.get('api_data', {})
            if not api_data or not isinstance(api_data, list) or len(api_data) == 0:
                logger.warning("No valid API data found for amenity checking")
                return False

            hotel_data = api_data[0]  # API returns a list with one hotel object
            amenity_summary = hotel_data.get('amenity_summary', {})

            # Extract all amenities into a searchable format
            all_amenities = []
            amenity_details = {}  # Store detailed amenity info

            # Get property-level amenities
            property_amenities = amenity_summary.get('property_amenities', [])
            if isinstance(property_amenities, list):
                for amenity_obj in property_amenities:
                    if isinstance(amenity_obj, dict) and 'amenity_key' in amenity_obj:
                        amenity_key = amenity_obj['amenity_key'].lower()
                        all_amenities.append(amenity_key)
                        amenity_details[amenity_key] = {
                            'type': 'property',
                            'category': amenity_obj.get('category', ''),
                            'original_key': amenity_obj['amenity_key']
                        }

            # Get room-level amenities from all room types
            room_types = ['ACACIA', 'MAPLE', 'OAK', 'MAHOGANY']  # Common Treebo room types
            for room_type in room_types:
                room_amenities = amenity_summary.get(room_type, [])
                if isinstance(room_amenities, list):
                    for amenity_obj in room_amenities:
                        if isinstance(amenity_obj, dict) and 'amenity_key' in amenity_obj:
                            amenity_key = amenity_obj['amenity_key'].lower()
                            if amenity_key not in all_amenities:  # Avoid duplicates
                                all_amenities.append(amenity_key)
                                amenity_details[amenity_key] = {
                                    'type': 'room',
                                    'room_type': room_type,
                                    'category': amenity_obj.get('category', ''),
                                    'original_key': amenity_obj['amenity_key']
                                }

            # Create searchable text from all amenities
            amenity_text = ' '.join(all_amenities)

            logger.info(f"Property has {len(all_amenities)} amenities: {', '.join(all_amenities[:15])}{'...' if len(all_amenities) > 15 else ''}")

            # Check each required amenity
            matched_amenities = []
            for amenity in required_amenities:
                if self._check_single_amenity(amenity_text, amenity):
                    matched_amenities.append(amenity)
                    logger.info(f"✅ Found amenity '{amenity}'")
                else:
                    logger.info(f"❌ Amenity '{amenity}' not found")
                    return False

            # If we get here, all amenities were found
            logger.info(f"🎉 All required amenities found: {matched_amenities}")
            return True

        except Exception as e:
            logger.error(f"Error checking amenities: {str(e)}")
            return False

    def _check_single_amenity(self, amenity_text: str, amenity: str) -> bool:
        """
        Check if a single amenity exists in the amenity text using comprehensive API-specific keywords
        """
        amenity_lower = amenity.lower().strip()

        # Treebo Brand Standards - Guaranteed amenities available across all properties
        guaranteed_amenities = {
            'wifi', 'wi-fi', 'internet', 'wireless',  # WiFi is standard across all Treebo hotels
            'breakfast', 'free_breakfast',  # Free breakfast is a Treebo standard
            'geyser', 'hot_water',  # Hot water/geyser is standard
            'security', '24_hour_security'  # Security is standard
        }

        # Check if this is a guaranteed amenity (more comprehensive check)
        for guaranteed_amenity in guaranteed_amenities:
            if guaranteed_amenity in amenity_lower or amenity_lower in guaranteed_amenity:
                logger.info(f"✅ Found guaranteed Treebo amenity: '{amenity}' (matched: {guaranteed_amenity})")
                return True

        # Comprehensive amenity keywords based on actual API response analysis
        amenity_keywords = {
            # Basic amenities (very common)
            'breakfast': ['breakfast', 'free_breakfast', 'complimentary_breakfast'],
            'wifi': ['wifi', 'wi_fi', 'internet', 'wireless', 'free_wifi'],
            'ac': ['ac_room', 'air_conditioning', 'ac', 'aircon', 'lobby_ac'],
            'tv': ['flat_screen_tv', 'television', 'tv', 'cable_tv'],
            'geyser': ['geyser', 'hot_water'],
            'fan': ['fan'],

            # Hotel services
            'elevator': ['elevator', 'lift'],
            'room_service': ['room_service', 'room_services'],
            'laundry': ['laundry', 'guest_laundry', 'dry_cleaning', 'washing'],
            'security': ['24_hour_security', 'security'],
            'card_payment': ['card_payment_accepted', 'card_payment'],
            'ironing': ['ironing_board', 'ironing'],

            # Room amenities
            'bed': ['queen_bed', 'single_beds', 'bed'],
            'toiletries': ['complimentary_toiletries', 'toiletries'],
            'furniture': ['other_furniture', 'lobby_furniture', 'sofa_chair', 'study_table_chair', 'coffee_table'],
            'cupboards': ['cupboards', 'storage'],
            'luggage': ['luggage_shelf', 'luggage'],
            'windows': ['windows'],
            'intercom': ['intercom'],
            'lock': ['lock', 'security_lock'],

            # Facilities
            'parking': ['parking', 'valet_parking', 'car_park', 'vehicle_parking'],
            'restaurant': ['restaurant', 'dining', 'food', 'in_house_restaurant'],
            'pantry': ['pantry', 'kitchen'],
            'travel_desk': ['travel_desk', 'travel'],
            'wheelchair': ['wheel_chair', 'wheelchair', 'accessibility'],

            # Safety
            'smoke_alarm': ['smoke_alarm', 'lobby_smoke_alarm', 'fire_safety'],
            'mosquito_repellent': ['mosquito_repellent', 'pest_control'],

            # Premium amenities (less common)
            'swimming_pool': ['swimming_pool', 'pool', 'swimming', 'swim'],
            'spa': ['spa', 'massage', 'wellness', 'ayurveda', 'spa_services'],
            'banquet': ['banquet', 'conference', 'meeting_room', 'event_hall', 'function_hall', 'banquet_hall'],
            'gym': ['gym', 'fitness', 'exercise', 'workout', 'fitness_center'],
            'smoking_room': ['smoking_room', 'smoking'],
            'public_washroom': ['public_washroom', 'washroom'],

            # General terms
            'luxury': ['luxury', 'premium', 'deluxe', 'suite'],
            'comfort': ['comfort', 'comfortable'],
            'clean': ['clean', 'cleaning', 'housekeeping']
        }

        # Get keywords for this amenity
        keywords = amenity_keywords.get(amenity_lower, [amenity_lower])

        # Check if any keyword is found
        for keyword in keywords:
            if keyword in amenity_text:
                return True

        # Also check for partial matches for compound words
        if '_' in amenity_lower:
            # Try matching parts of compound amenities
            parts = amenity_lower.split('_')
            for part in parts:
                if len(part) > 2 and part in amenity_text:
                    return True

        return False



    def _get_city_aliases(self) -> Dict[str, List[str]]:
        """Get comprehensive city alias mapping"""
        return {
            # Major Metro Cities
            'mumbai': ['Mumbai'],
            'bombay': ['Mumbai'],
            'bom': ['Mumbai'],

            'bangalore': ['Bangalore'],
            'bengaluru': ['Bangalore'],
            'blr': ['Bangalore'],

            'delhi': ['Delhi', 'New Delhi'],
            'new delhi': ['New Delhi', 'Delhi'],
            'gurgaon': ['Gurgaon'],
            'gurugram': ['Gurgaon'],
            'noida': ['Noida'],
            'faridabad': ['Faridabad'],

            'chennai': ['Chennai'],
            'madras': ['Chennai'],

            'kolkata': ['Kolkata'],
            'calcutta': ['Kolkata'],

            'hyderabad': ['Hyderabad'],
            'secunderabad': ['Hyderabad'],
            'cyberabad': ['Hyderabad'],

            'pune': ['Pune'],
            'poona': ['Pune'],

            # Other major cities with aliases
            'kochi': ['Kochi'],
            'cochin': ['Kochi'],
            'thiruvananthapuram': ['Thiruvananthapuram'],
            'trivandrum': ['Thiruvananthapuram'],
            'mysore': ['Mysore'],
            'mysuru': ['Mysore'],
            'mangalore': ['Mangalore'],
            'mangaluru': ['Mangalore'],
            'vadodara': ['Vadodara'],
            'baroda': ['Vadodara'],
            'visakhapatnam': ['Visakhapatnam'],
            'vizag': ['Visakhapatnam'],
            'coimbatore': ['Coimbatore'],
            'cbe': ['Coimbatore'],
            'pondicherry': ['Pondicherry'],
            'puducherry': ['Pondicherry'],
            'goa': ['Goa'],
            'panaji': ['Goa'],
            'margao': ['Goa']
        }

    def get_hotel_by_id(self, property_id: int) -> Optional[Dict[str, Any]]:
        """Get hotel by property ID"""
        try:
            query = """
            SELECT * FROM hotel_metadata
            WHERE property_id = %s AND status = 'LIVE'
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (property_id,))
                result = cursor.fetchone()
                return dict(result) if result else None

        except Exception as e:
            logger.error(f"Error getting hotel by ID: {str(e)}")
            return None

    def search_hotels_by_name(self, name: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search hotels by name with fuzzy matching"""
        try:
            query = """
            SELECT
                property_id, name, city_name, locality_name,
                SIMILARITY(name, %s) as similarity_score
            FROM hotel_metadata
            WHERE status = 'LIVE'
            AND (
                LOWER(name) LIKE %s
                OR SIMILARITY(name, %s) > %s
            )
            ORDER BY similarity_score DESC, name
            LIMIT %s
            """

            search_term = f"%{name.lower()}%"

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                similarity_threshold = Config.SIMILARITY_THRESHOLD
                cursor.execute(query, (name, search_term, name, similarity_threshold, limit))
                results = cursor.fetchall()
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error searching hotels by name: {str(e)}")
            # Fallback to simple LIKE search
            return self._simple_name_search(name, limit)

    def _simple_name_search(self, name: str, limit: int) -> List[Dict[str, Any]]:
        """Simple name search fallback"""
        try:
            query = """
            SELECT property_id, name, city_name, locality_name
            FROM hotel_metadata
            WHERE status = 'LIVE' AND LOWER(name) LIKE %s
            ORDER BY name
            LIMIT %s
            """

            search_term = f"%{name.lower()}%"

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (search_term, limit))
                results = cursor.fetchall()
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error in simple name search: {str(e)}")
            return []

    def get_hotels_by_city(self, city: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get all hotels in a specific city"""
        try:
            query = """
            SELECT * FROM hotel_metadata
            WHERE status = 'LIVE' AND LOWER(city_name) = %s
            ORDER BY name
            LIMIT %s
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (city.lower(), limit))
                results = cursor.fetchall()
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error getting hotels by city: {str(e)}")
            return []

    def get_hotel_by_id(self, property_id: int) -> Dict[str, Any]:
        """Get hotel details by property ID"""
        try:
            self._ensure_connection()

            query = """
            SELECT property_id, name, city_name, locality_name,
                   postal_address, maps_link, status
            FROM hotel_metadata
            WHERE property_id = %s AND status = 'LIVE'
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (property_id,))
                result = cursor.fetchone()

                if result:
                    return dict(result)
                else:
                    return None

        except Exception as e:
            logger.error(f"Error getting hotel by ID {property_id}: {str(e)}")
            return None

    def get_nearby_hotels(self, latitude: float, longitude: float, radius_km: float = 10, limit: int = 20) -> List[Dict[str, Any]]:
        """Get hotels within a radius of given coordinates"""
        try:
            query = """
            SELECT *,
                (6371 * acos(cos(radians(%s)) * cos(radians(latitude)) *
                cos(radians(longitude) - radians(%s)) + sin(radians(%s)) *
                sin(radians(latitude)))) AS distance_km
            FROM hotel_metadata
            WHERE status = 'LIVE'
            AND latitude IS NOT NULL
            AND longitude IS NOT NULL
            HAVING distance_km <= %s
            ORDER BY distance_km
            LIMIT %s
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query, (latitude, longitude, latitude, radius_km, limit))
                results = cursor.fetchall()
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error getting nearby hotels: {str(e)}")
            return []

    def get_analytics_data(self) -> Dict[str, Any]:
        """Get analytics data for dashboard"""
        try:
            analytics = {}

            # Total hotels by city
            query = """
            SELECT city_name, COUNT(*) as hotel_count
            FROM hotel_metadata
            WHERE status = 'LIVE'
            GROUP BY city_name
            ORDER BY hotel_count DESC
            LIMIT 10
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query)
                analytics['hotels_by_city'] = [dict(row) for row in cursor.fetchall()]

            # Total hotels by state (if available)
            try:
                query = """
                SELECT state_name, COUNT(*) as hotel_count
                FROM hotel_metadata
                WHERE status = 'LIVE' AND state_name IS NOT NULL
                GROUP BY state_name
                ORDER BY hotel_count DESC
                """

                cursor.execute(query)
                analytics['hotels_by_state'] = [dict(row) for row in cursor.fetchall()]
            except:
                analytics['hotels_by_state'] = []

            # Overall statistics
            query = """
            SELECT
                COUNT(*) as total_hotels,
                COUNT(DISTINCT city_name) as total_cities,
                COUNT(DISTINCT locality_name) as total_localities
            FROM hotel_metadata
            WHERE status = 'LIVE'
            """

            cursor.execute(query)
            stats = cursor.fetchone()
            analytics['overall_stats'] = dict(stats) if stats else {}

            return analytics

        except Exception as e:
            logger.error(f"Error getting analytics data: {str(e)}")
            return {}

    def execute_custom_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """Execute custom SQL query"""
        try:
            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                results = cursor.fetchall()
                return [dict(row) for row in results]

        except Exception as e:
            logger.error(f"Error executing custom query: {str(e)}")
            return []

    def close_connection(self):
        """Close database connections"""
        try:
            if self.connection:
                self.connection.close()
            if self.engine:
                self.engine.dispose()
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Error closing database connections: {str(e)}")

    def __del__(self):
        """Cleanup on object destruction"""
        self.close_connection()
