"""
Geographic Intelligence Service - AI-powered geographic logic and validation
Prevents logical impossibilities like "mountain view in Delhi" or "beach view in Bangalore"
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
import re

logger = logging.getLogger(__name__)

class GeographicIntelligenceService:
    def __init__(self):
        # Geographic features by city/region
        self.city_geographic_features = {
            # Plains cities (NO mountains, NO beaches)
            'delhi': {
                'terrain': 'plains',
                'features': ['urban', 'historical', 'government'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea', 'hill'],
                'possible_views': ['city', 'garden', 'park', 'monument'],
                'state': 'Delhi',
                'region': 'North India Plains'
            },
            'new delhi': {
                'terrain': 'plains',
                'features': ['urban', 'historical', 'government'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea', 'hill'],
                'possible_views': ['city', 'garden', 'park', 'monument'],
                'state': 'Delhi',
                'region': 'North India Plains'
            },
            'gurgaon': {
                'terrain': 'plains',
                'features': ['urban', 'business'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea'],
                'possible_views': ['city', 'garden', 'park'],
                'state': 'Haryana',
                'region': 'North India Plains'
            },
            'noida': {
                'terrain': 'plains',
                'features': ['urban', 'business'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea'],
                'possible_views': ['city', 'garden', 'park'],
                'state': 'Uttar Pradesh',
                'region': 'North India Plains'
            },

            # Coastal cities (beaches, NO mountains)
            'mumbai': {
                'terrain': 'coastal',
                'features': ['urban', 'business', 'coastal'],
                'impossible_views': ['mountain'],
                'possible_views': ['city', 'sea', 'ocean', 'beach', 'harbor'],
                'state': 'Maharashtra',
                'region': 'West Coast'
            },
            'goa': {
                'terrain': 'coastal',
                'features': ['beach', 'coastal', 'tourism'],
                'impossible_views': ['mountain'],
                'possible_views': ['beach', 'sea', 'ocean', 'palm', 'coastal'],
                'state': 'Goa',
                'region': 'West Coast'
            },
            'kochi': {
                'terrain': 'coastal',
                'features': ['coastal', 'backwater', 'port'],
                'impossible_views': ['mountain'],
                'possible_views': ['sea', 'backwater', 'harbor', 'coastal'],
                'state': 'Kerala',
                'region': 'South West Coast'
            },
            'chennai': {
                'terrain': 'coastal',
                'features': ['urban', 'coastal', 'business'],
                'impossible_views': ['mountain'],
                'possible_views': ['city', 'sea', 'beach', 'marina'],
                'state': 'Tamil Nadu',
                'region': 'East Coast'
            },

            # Mountain/Hill cities (mountains, NO beaches)
            'shimla': {
                'terrain': 'mountains',
                'features': ['mountains', 'hill_station', 'tourism'],
                'impossible_views': ['beach', 'ocean', 'sea'],
                'possible_views': ['mountain', 'valley', 'hill', 'forest', 'snow'],
                'state': 'Himachal Pradesh',
                'region': 'Himalayas'
            },
            'manali': {
                'terrain': 'mountains',
                'features': ['mountains', 'hill_station', 'adventure'],
                'impossible_views': ['beach', 'ocean', 'sea'],
                'possible_views': ['mountain', 'valley', 'river', 'snow', 'forest'],
                'state': 'Himachal Pradesh',
                'region': 'Himalayas'
            },
            'dharamshala': {
                'terrain': 'mountains',
                'features': ['mountains', 'hill_station', 'spiritual'],
                'impossible_views': ['beach', 'ocean', 'sea'],
                'possible_views': ['mountain', 'valley', 'forest'],
                'state': 'Himachal Pradesh',
                'region': 'Himalayas'
            },
            'ooty': {
                'terrain': 'hills',
                'features': ['hills', 'hill_station', 'tourism'],
                'impossible_views': ['beach', 'ocean', 'sea'],
                'possible_views': ['hill', 'valley', 'tea_garden', 'forest'],
                'state': 'Tamil Nadu',
                'region': 'Nilgiri Hills'
            },

            # Inland plateau cities (NO mountains, NO beaches)
            'bangalore': {
                'terrain': 'plateau',
                'features': ['urban', 'technology', 'garden'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea'],
                'possible_views': ['city', 'garden', 'park', 'lake'],
                'state': 'Karnataka',
                'region': 'Deccan Plateau'
            },
            'pune': {
                'terrain': 'plateau',
                'features': ['urban', 'education', 'business'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea'],
                'possible_views': ['city', 'garden', 'park'],
                'state': 'Maharashtra',
                'region': 'Deccan Plateau'
            },
            'hyderabad': {
                'terrain': 'plateau',
                'features': ['urban', 'technology', 'historical'],
                'impossible_views': ['mountain', 'beach', 'ocean', 'sea'],
                'possible_views': ['city', 'lake', 'garden'],
                'state': 'Telangana',
                'region': 'Deccan Plateau'
            },
            'kolkata': {
                'terrain': 'plains',
                'features': ['urban', 'cultural', 'river'],
                'impossible_views': ['mountain', 'beach', 'ocean'],
                'possible_views': ['city', 'river', 'garden'],
                'state': 'West Bengal',
                'region': 'Gangetic Plains'
            }
        }

        # View type classifications
        self.view_types = {
            'mountain': ['mountain', 'hill', 'peak', 'valley', 'alpine', 'himalayan'],
            'beach': ['beach', 'ocean', 'sea', 'coastal', 'shore', 'seaside'],
            'city': ['city', 'urban', 'skyline', 'building'],
            'garden': ['garden', 'park', 'green', 'lawn'],
            'water': ['lake', 'river', 'pond', 'backwater'],
            'special': ['monument', 'historical', 'heritage']
        }

    def validate_query_logic(self, query: str, cities: List[str], amenities: List[str]) -> Dict[str, Any]:
        """
        Validate if the query makes geographic sense using AI intelligence
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': [],
            'logic_score': 1.0
        }

        if not cities:
            return validation_result

        # Extract view requirements from query and amenities
        view_requirements = self._extract_view_requirements(query, amenities)

        if not view_requirements:
            return validation_result

        # Validate each city against view requirements
        for city in cities:
            city_validation = self._validate_city_view_combination(city, view_requirements)

            if not city_validation['is_valid']:
                validation_result['is_valid'] = False
                validation_result['errors'].extend(city_validation['errors'])
                validation_result['suggestions'].extend(city_validation['suggestions'])
                validation_result['logic_score'] *= 0.1  # Severe penalty for logic errors

        return validation_result

    def _extract_view_requirements(self, query: str, amenities: List[str]) -> List[str]:
        """Extract view requirements from query and amenities"""
        query_lower = query.lower()
        view_requirements = []

        # Check for view keywords in query
        for view_type, keywords in self.view_types.items():
            for keyword in keywords:
                if keyword in query_lower:
                    view_requirements.append(view_type)
                    break

        # Check for view keywords in amenities
        for amenity in amenities:
            amenity_lower = amenity.lower()
            for view_type, keywords in self.view_types.items():
                for keyword in keywords:
                    if keyword in amenity_lower:
                        view_requirements.append(view_type)
                        break

        return list(set(view_requirements))  # Remove duplicates

    def _validate_city_view_combination(self, city: str, view_requirements: List[str]) -> Dict[str, Any]:
        """Validate if a city can provide the required views"""
        city_lower = city.lower().strip()

        # Get city geographic data
        city_data = self.city_geographic_features.get(city_lower)

        if not city_data:
            # Unknown city - allow with warning
            return {
                'is_valid': True,
                'errors': [],
                'warnings': [f"Unknown geographic data for city: {city}"],
                'suggestions': []
            }

        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }

        impossible_views = city_data.get('impossible_views', [])
        possible_views = city_data.get('possible_views', [])
        terrain = city_data.get('terrain', 'unknown')
        state = city_data.get('state', 'unknown')

        # Check for impossible combinations
        for view_req in view_requirements:
            if view_req in impossible_views:
                validation_result['is_valid'] = False

                # Generate specific error messages
                if view_req == 'mountain' and terrain in ['plains', 'coastal', 'plateau']:
                    validation_result['errors'].append(
                        f"LOGIC ERROR: {city} ({state}) is in {terrain} region - NO mountains available for mountain view"
                    )

                    # Suggest mountain cities
                    mountain_cities = [c for c, data in self.city_geographic_features.items()
                                     if 'mountain' in data.get('possible_views', [])]
                    validation_result['suggestions'].append(
                        f"For mountain view, try: {', '.join(mountain_cities[:3])}"
                    )

                elif view_req == 'beach' and terrain in ['plains', 'mountains', 'plateau']:
                    validation_result['errors'].append(
                        f"LOGIC ERROR: {city} ({state}) is {terrain} region - NO beaches available for beach/ocean view"
                    )

                    # Suggest coastal cities
                    coastal_cities = [c for c, data in self.city_geographic_features.items()
                                    if 'beach' in data.get('possible_views', [])]
                    validation_result['suggestions'].append(
                        f"For beach view, try: {', '.join(coastal_cities[:3])}"
                    )

        return validation_result

    def get_alternative_suggestions(self, invalid_query: str, cities: List[str], view_requirements: List[str]) -> List[str]:
        """Generate intelligent alternative suggestions for invalid queries"""
        suggestions = []

        for view_req in view_requirements:
            if view_req == 'mountain':
                mountain_cities = [c for c, data in self.city_geographic_features.items()
                                 if 'mountain' in data.get('possible_views', [])]
                suggestions.append(f"For mountain view: {', '.join(mountain_cities)}")

            elif view_req == 'beach':
                coastal_cities = [c for c, data in self.city_geographic_features.items()
                                if 'beach' in data.get('possible_views', [])]
                suggestions.append(f"For beach view: {', '.join(coastal_cities)}")

        # Suggest what IS possible in the requested cities
        for city in cities:
            city_data = self.city_geographic_features.get(city.lower())
            if city_data:
                possible_views = city_data.get('possible_views', [])
                if possible_views:
                    suggestions.append(f"In {city}, try: {', '.join(possible_views)}")

        return suggestions

    def is_geographic_combination_possible(self, city: str, amenity: str) -> bool:
        """Quick check if a city-amenity combination is geographically possible"""
        city_lower = city.lower().strip()
        amenity_lower = amenity.lower().strip()

        city_data = self.city_geographic_features.get(city_lower)
        if not city_data:
            return True  # Unknown city, allow

    def get_implicit_amenities_for_city(self, city: str) -> List[str]:
        """
        Get amenities that are IMPLICITLY available in a city due to geography
        This is CRITICAL for mountain cities where mountain view is GUARANTEED
        """
        city_lower = city.lower().strip()
        city_data = self.city_geographic_features.get(city_lower)

        if not city_data:
            return []

        implicit_amenities = []
        terrain = city_data.get('terrain', '')
        possible_views = city_data.get('possible_views', [])

        # Mountain cities AUTOMATICALLY have mountain view
        if terrain == 'mountains' or 'mountain' in possible_views:
            implicit_amenities.extend(['mountain_view', 'mountain', 'hill_view', 'valley_view'])
            logger.info(f"🏔️ IMPLICIT AMENITY: {city} is mountain city - mountain view GUARANTEED")

        # Hill stations AUTOMATICALLY have mountain/hill view
        if terrain == 'hills' or 'hill' in possible_views:
            implicit_amenities.extend(['hill_view', 'mountain_view', 'valley_view'])
            logger.info(f"🏔️ IMPLICIT AMENITY: {city} is hill station - hill view GUARANTEED")

        # Coastal cities AUTOMATICALLY have sea/beach view
        if terrain == 'coastal' or 'beach' in possible_views:
            implicit_amenities.extend(['sea_view', 'ocean_view', 'beach_view', 'coastal_view'])
            logger.info(f"🌊 IMPLICIT AMENITY: {city} is coastal - sea view GUARANTEED")

        return implicit_amenities

    def does_city_provide_view_naturally(self, city: str, view_type: str) -> bool:
        """
        Check if a city naturally provides a view type due to its geography
        CRITICAL for basic searches like "mountain view in Himachal"
        """
        implicit_amenities = self.get_implicit_amenities_for_city(city)
        view_lower = view_type.lower()

        # Check if the requested view is naturally available
        for implicit_amenity in implicit_amenities:
            if view_lower in implicit_amenity or implicit_amenity in view_lower:
                logger.info(f"✅ NATURAL VIEW: {city} naturally provides {view_type}")
                return True

        return False

        impossible_views = city_data.get('impossible_views', [])

        # Check if amenity contains impossible view keywords
        for impossible_view in impossible_views:
            if impossible_view in amenity_lower:
                return False

        return True
