import pandas as pd
import requests
import logging
from typing import Dict, List, Any, Optional
from fuzzywuzzy import fuzz, process
import os
import json
import time
from config import Config

logger = logging.getLogger(__name__)

class HotelService:
    """
    Enhanced service for managing hotel data and API interactions
    """

    def __init__(self, db_service=None):
        self.db_service = db_service
        self.hotels_df = None
        self.api_base_url = Config.TREEBO_API_BASE_URL

        # NO CACHE POLICY - ALWAYS FRESH DATA
        self._api_cache = {}
        self._cache_ttl = 0  # NO CACHE - ALWAYS FETCH FRESH DATA
        self._cache_timestamps = {}
        self._no_stale_data_policy = True

        # Force clear ALL cache on initialization
        self.force_clear_all_cache()
        logger.warning("🚫 NO STALE DATA POLICY: Cache disabled - always fetching fresh data")

        # Initialize brand standards service
        self.brand_standards_service = None
        if db_service and hasattr(db_service, 'connection'):
            try:
                from .brand_standards_service import BrandStandardsService
                self.brand_standards_service = BrandStandardsService(db_service.connection)
                logger.info("Brand standards service initialized successfully")
            except Exception as e:
                logger.warning(f"Could not initialize brand standards service: {str(e)}")
                self.brand_standards_service = None

        # Enhanced amenity mapping for better API parsing
        self.enhanced_amenity_mapping = {
            'swimming_pool': [
                'swimming pool', 'pool', 'swimming', 'swim', 'aquatic',
                'water sports', 'pool area', 'swimming facility'
            ],
            'spa': [
                'spa', 'massage', 'wellness', 'therapy', 'ayurveda',
                'beauty salon', 'wellness center', 'spa services'
            ],
            'banquet': [
                'banquet', 'banquet hall', 'conference room', 'meeting room',
                'event hall', 'conference', 'meeting', 'function hall',
                'party hall', 'celebration hall', 'wedding hall'
            ],
            'parking': [
                'parking', 'valet parking', 'car park', 'vehicle parking',
                'garage', 'parking space', 'parking facility'
            ],
            'breakfast': [
                'breakfast', 'complimentary breakfast', 'morning meal',
                'breakfast service', 'breakfast buffet', 'continental breakfast'
            ],
            'elevator': [
                'elevator', 'lift', 'escalator', 'vertical transport'
            ],
            'laundry': [
                'laundry', 'dry cleaning', 'washing', 'laundry service',
                'clothes cleaning', 'ironing'
            ],
            'wifi': [
                'wifi', 'internet', 'wireless', 'wi-fi', 'broadband',
                'internet access', 'wireless internet'
            ],
            'gym': [
                'gym', 'fitness center', 'exercise', 'workout', 'fitness',
                'gymnasium', 'fitness facility', 'health club'
            ],
            'restaurant': [
                'restaurant', 'dining', 'food', 'cuisine', 'cafe',
                'coffee shop', 'dining room', 'food court'
            ],
            'room_service': [
                'room service', 'in-room dining', 'room dining'
            ],
            'ac': [
                'air conditioning', 'ac', 'air conditioner', 'cooling',
                'climate control', 'hvac'
            ],
            'tv': [
                'television', 'tv', 'cable tv', 'satellite tv',
                'entertainment', 'lcd tv', 'led tv'
            ],
            'twin_bed': [
                'twin bed', 'twin beds', 'two beds', 'separate beds',
                'twin bedding'
            ],
            'double_bed': [
                'double bed', 'king bed', 'queen bed', 'large bed',
                'king size', 'queen size'
            ],
            'balcony': [
                'balcony', 'terrace', 'outdoor space', 'patio',
                'veranda', 'deck'
            ],
            'view': [
                'view', 'scenic view', 'mountain view', 'city view',
                'lake view', 'sea view', 'garden view', 'river view'
            ]
        }

        # Amenity mapping for API responses
        self.amenity_mapping = {
            'swimming_pool': ['swimming pool', 'pool', 'swimming'],
            'spa': ['spa', 'massage', 'wellness'],
            'banquet': ['banquet', 'banquet hall', 'conference room', 'meeting room'],
            'parking': ['parking', 'valet parking', 'car park'],
            'breakfast': ['breakfast', 'complimentary breakfast'],
            'elevator': ['elevator', 'lift'],
            'laundry': ['laundry', 'dry cleaning'],
            'wifi': ['wifi', 'internet', 'wireless'],
            'gym': ['gym', 'fitness center', 'exercise'],
            'restaurant': ['restaurant', 'dining'],
            'room_service': ['room service'],
            'ac': ['air conditioning', 'ac'],
            'tv': ['television', 'tv', 'cable tv'],
            'twin_bed': ['twin bed', 'twin beds'],
            'double_bed': ['double bed', 'king bed', 'queen bed'],
            'balcony': ['balcony', 'terrace'],
            'view': ['view', 'scenic view']
        }

    def load_hotel_data(self):
        """
        Load hotel data from CSV file
        """
        try:
            csv_path = 'hotel_metadata.csv'
            if not os.path.exists(csv_path):
                raise FileNotFoundError(f"Hotel metadata file not found: {csv_path}")

            self.hotels_df = pd.read_csv(csv_path)

            # Clean and standardize data
            self.hotels_df['name'] = self.hotels_df['name'].str.strip()
            self.hotels_df['city_name'] = self.hotels_df['city_name'].str.strip()
            self.hotels_df['status'] = self.hotels_df['status'].str.strip()

            # Filter only LIVE properties
            self.hotels_df = self.hotels_df[self.hotels_df['status'] == 'LIVE']

            logger.info(f"Loaded {len(self.hotels_df)} live hotels from CSV")

        except Exception as e:
            logger.error(f"Error loading hotel data: {str(e)}")
            raise

    def get_all_hotels(self) -> List[Dict[str, Any]]:
        """
        Get all hotels with basic information
        """
        if self.hotels_df is None:
            self.load_hotel_data()

        hotels = []
        for _, row in self.hotels_df.iterrows():
            hotels.append({
                'property_id': int(row['property_id']),
                'name': row['name'],
                'city_name': row['city_name'],
                'locality_name': row['locality_name'],
                'micro_market_name': row['micro_market_name'],
                'status': row['status'],
                'latitude': row['latitude'] if pd.notna(row['latitude']) else None,
                'longitude': row['longitude'] if pd.notna(row['longitude']) else None,
                'postal_address': row['postal_address'] if pd.notna(row['postal_address']) else None,
                'maps_link': row['maps_link'] if pd.notna(row['maps_link']) else None
            })

        return hotels

    def get_all_cities(self) -> List[str]:
        """
        Get all unique cities
        """
        if self.hotels_df is None:
            self.load_hotel_data()

        cities = self.hotels_df['city_name'].dropna().unique().tolist()
        return sorted(cities)

    def get_available_amenities(self) -> List[str]:
        """
        Get all available amenity types
        """
        return list(self.amenity_mapping.keys())

    def get_hotel_details(self, property_id: int) -> Optional[Dict[str, Any]]:
        """
        Get detailed hotel information from API with enhanced amenity parsing
        """
        try:
            # NO STALE DATA POLICY - ALWAYS FETCH FRESH DATA
            if self._no_stale_data_policy:
                # Force clear any existing cache for this property
                if property_id in self._api_cache:
                    del self._api_cache[property_id]
                    if property_id in self._cache_timestamps:
                        del self._cache_timestamps[property_id]
                    logger.warning(f"🚫 NO STALE DATA: Cleared cache for property {property_id}")

                logger.warning(f"🔄 REAL-TIME FETCH: Getting fresh data for property {property_id} (no cache)")
            else:
                # Legacy cache logic (disabled by default)
                current_time = time.time()
                if property_id in self._api_cache:
                    cache_timestamp = self._cache_timestamps.get(property_id, 0)
                    if current_time - cache_timestamp < self._cache_ttl:
                        cache_age = int(current_time - cache_timestamp)
                        logger.info(f"Returning cached data for property {property_id} (age: {cache_age}s)")
                        return self._api_cache[property_id]

            # Make API call
            url = f"{self.api_base_url}?property_id={property_id}"
            logger.info(f"Making API call to: {url}")

            response = requests.get(url, timeout=Config.API_TIMEOUT)
            response.raise_for_status()

            api_data = response.json()

            # Get basic hotel info from database/CSV
            if self.db_service:
                hotel_info = self.db_service.get_hotel_by_id(property_id)
            else:
                hotel_info = self._get_hotel_from_csv(property_id)

            # Process amenity_summary from API response
            processed_hotel_data = self._process_amenity_summary(api_data, hotel_info)

            # Combine all data
            detailed_info = {
                'property_id': property_id,
                'basic_info': hotel_info,
                'api_data': api_data,
                'property_amenities': processed_hotel_data.get('property_amenities', {}),
                'room_amenities': processed_hotel_data.get('room_amenities', {}),
                'room_types': self._extract_room_types(api_data),
                'amenity_summary': processed_hotel_data.get('amenity_summary', {}),
                'brand_standards_applied': processed_hotel_data.get('brand_standards_applied', False)
            }

            # NO STALE DATA POLICY - DO NOT CACHE
            if not self._no_stale_data_policy:
                # Legacy caching (disabled by default)
                self._api_cache[property_id] = detailed_info
                self._cache_timestamps[property_id] = time.time()
                logger.info(f"Cached fresh data for property {property_id}")
            else:
                logger.warning(f"🚫 NO CACHE: Fresh data fetched for property {property_id} - not cached to ensure no stale data")

            return detailed_info

        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed for property {property_id}: {str(e)}")
            # Return basic info if API fails
            hotel_info = self.db_service.get_hotel_by_id(property_id) if self.db_service else self._get_hotel_from_csv(property_id)
            return {
                'property_id': property_id,
                'basic_info': hotel_info,
                'api_data': None,
                'property_amenities': {},
                'room_amenities': {},
                'room_types': [],
                'error': f"API unavailable: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Error getting hotel details for {property_id}: {str(e)}")
            return None

    def search_hotels(self, city: str = None, amenities: List[str] = None,
                     property_name: str = None) -> List[Dict[str, Any]]:
        """
        Search hotels based on filters
        """
        if self.hotels_df is None:
            self.load_hotel_data()

        # Start with all hotels
        filtered_df = self.hotels_df.copy()

        # Filter by city with exact matching and alias support
        if city:
            # Get city aliases
            city_aliases = self._get_city_aliases()
            target_cities = []

            # Check if the input city is an alias
            city_lower = city.lower()
            if city_lower in city_aliases:
                target_cities.extend(city_aliases[city_lower])
            else:
                target_cities.append(city)

            # Exact city name matching (case insensitive)
            city_matches = filtered_df['city_name'].str.lower().isin([c.lower() for c in target_cities])
            filtered_df = filtered_df[city_matches]

        # Filter by property name
        if property_name:
            name_matches = filtered_df['name'].str.contains(property_name, case=False, na=False)
            filtered_df = filtered_df[name_matches]

        # Convert to list of dictionaries
        results = []
        for _, row in filtered_df.iterrows():
            hotel_data = {
                'property_id': int(row['property_id']),
                'name': row['name'],
                'city_name': row['city_name'],
                'locality_name': row['locality_name'],
                'micro_market_name': row['micro_market_name'],
                'postal_address': row['postal_address'] if pd.notna(row['postal_address']) else None,
                'maps_link': row['maps_link'] if pd.notna(row['maps_link']) else None,
                'latitude': row['latitude'] if pd.notna(row['latitude']) else None,
                'longitude': row['longitude'] if pd.notna(row['longitude']) else None
            }

            # If amenities are specified, check them via API
            if amenities:
                hotel_details = self.get_hotel_details(int(row['property_id']))
                if hotel_details and self._has_amenities(hotel_details, amenities):
                    hotel_data['matched_amenities'] = amenities
                    hotel_data['amenity_details'] = hotel_details.get('amenities', {})
                    results.append(hotel_data)
            else:
                results.append(hotel_data)

        logger.info(f"Search returned {len(results)} hotels")
        return results

    def _get_hotel_from_csv(self, property_id: int) -> Optional[Dict[str, Any]]:
        """
        Get hotel basic info from CSV data
        """
        if self.hotels_df is None:
            return None

        hotel_row = self.hotels_df[self.hotels_df['property_id'] == property_id]

        if hotel_row.empty:
            return None

        row = hotel_row.iloc[0]
        return {
            'property_id': int(row['property_id']),
            'name': row['name'],
            'city_name': row['city_name'],
            'locality_name': row['locality_name'],
            'micro_market_name': row['micro_market_name'],
            'status': row['status'],
            'postal_address': row['postal_address'] if pd.notna(row['postal_address']) else None,
            'maps_link': row['maps_link'] if pd.notna(row['maps_link']) else None,
            'latitude': row['latitude'] if pd.notna(row['latitude']) else None,
            'longitude': row['longitude'] if pd.notna(row['longitude']) else None
        }

    def _process_amenity_summary(self, api_data: Dict[str, Any], hotel_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process amenity_summary from API response with brand standards integration
        """
        processed_data = {
            'property_amenities': {},
            'room_amenities': {},
            'amenity_summary': {},
            'brand_standards_applied': False
        }

        try:
            # Extract amenity_summary from API data
            if isinstance(api_data, list) and len(api_data) > 0:
                hotel_api_data = api_data[0]
            else:
                hotel_api_data = api_data

            amenity_summary = hotel_api_data.get('amenity_summary', {}) if isinstance(hotel_api_data, dict) else {}

            if amenity_summary and self.brand_standards_service:
                # Enhance with brand standards
                enhanced_summary = self.brand_standards_service.enhance_amenity_summary(amenity_summary)
                processed_hotel_data = self.brand_standards_service.process_hotel_amenities(
                    {'amenity_summary': enhanced_summary},
                    brand='treebo'
                )

                processed_data.update(processed_hotel_data)
                processed_data['brand_standards_applied'] = True

                logger.info(f"Applied brand standards to hotel {hotel_info.get('property_id', 'unknown')}")

            elif amenity_summary:
                # Process without brand standards service
                processed_data['amenity_summary'] = amenity_summary
                processed_data['property_amenities'] = self._extract_property_amenities_from_summary(amenity_summary)
                processed_data['room_amenities'] = self._extract_room_amenities_from_summary(amenity_summary)

                logger.info(f"Processed amenity summary without brand standards for hotel {hotel_info.get('property_id', 'unknown')}")

            else:
                # No amenity summary available, provide basic guaranteed amenities
                if self.brand_standards_service:
                    guaranteed = self.brand_standards_service.get_guaranteed_amenities('treebo')
                    processed_data['property_amenities'] = {
                        key: {
                            'name': details['name'],
                            'category': details['category'],
                            'guaranteed': True,
                            'brand_standard': True
                        }
                        for key, details in guaranteed.items()
                    }
                    processed_data['brand_standards_applied'] = True

                logger.warning(f"No amenity summary available for hotel {hotel_info.get('property_id', 'unknown')}, using brand standards")

        except Exception as e:
            logger.error(f"Error processing amenity summary: {str(e)}")

        return processed_data

    def _extract_property_amenities_from_summary(self, amenity_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Extract property amenities from amenity_summary structure"""
        property_amenities = {}

        property_amenity_list = amenity_summary.get('property_amenities', [])
        for amenity in property_amenity_list:
            if isinstance(amenity, dict) and 'amenity_key' in amenity:
                amenity_key = amenity['amenity_key']
                property_amenities[amenity_key] = {
                    'name': amenity.get('amenity_name', amenity_key),
                    'category': amenity.get('category', 'other'),
                    'available': True
                }

        return property_amenities

    def _extract_room_amenities_from_summary(self, amenity_summary: Dict[str, Any]) -> Dict[str, Any]:
        """Extract room amenities from amenity_summary structure"""
        room_amenities = {}
        room_types = ['ACACIA', 'MAPLE', 'OAK', 'MAHOGANY']

        for room_type in room_types:
            room_amenity_list = amenity_summary.get(room_type, [])
            if room_amenity_list:
                room_amenities[room_type.lower()] = {}
                for amenity in room_amenity_list:
                    if isinstance(amenity, dict) and 'amenity_key' in amenity:
                        amenity_key = amenity['amenity_key']
                        room_amenities[room_type.lower()][amenity_key] = {
                            'name': amenity.get('amenity_name', amenity_key),
                            'category': amenity.get('category', 'room'),
                            'available': True
                        }

        return room_amenities

    def _extract_detailed_amenities(self, api_data: Dict[str, Any]) -> tuple:
        """
        Extract amenities from API response, separating property and room level amenities
        """
        property_amenities = {}
        room_amenities = {}

        try:
            if not isinstance(api_data, dict):
                return property_amenities, room_amenities

            # Extract property-level amenities
            property_fields = [
                'amenities', 'facilities', 'services', 'features',
                'property_amenities', 'hotel_amenities', 'common_amenities'
            ]

            for field in property_fields:
                if field in api_data and api_data[field]:
                    property_amenities[field] = api_data[field]

            # Extract room-level amenities by room type
            if 'rooms' in api_data or 'room_types' in api_data:
                rooms_data = api_data.get('rooms', api_data.get('room_types', []))

                if isinstance(rooms_data, list):
                    for room in rooms_data:
                        if isinstance(room, dict):
                            room_type = room.get('room_type', room.get('type', room.get('name', 'unknown')))

                            # Handle specific room types: acacia, oak, maple, mahogany
                            room_type_lower = room_type.lower()
                            if any(rt in room_type_lower for rt in ['acacia', 'oak', 'maple', 'mahogany']):
                                room_amenities[room_type] = {
                                    'amenities': room.get('amenities', []),
                                    'features': room.get('features', []),
                                    'facilities': room.get('facilities', []),
                                    'services': room.get('services', [])
                                }
                elif isinstance(rooms_data, dict):
                    # Handle case where rooms_data is a dictionary
                    for room_type, room_data in rooms_data.items():
                        if isinstance(room_data, dict):
                            room_amenities[room_type] = {
                                'amenities': room_data.get('amenities', []),
                                'features': room_data.get('features', []),
                                'facilities': room_data.get('facilities', []),
                                'services': room_data.get('services', [])
                            }

        except Exception as e:
            logger.error(f"Error extracting detailed amenities from API data: {str(e)}")

        return property_amenities, room_amenities

    def _extract_room_types(self, api_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract room types with their details
        """
        room_types = []

        try:
            if 'rooms' in api_data or 'room_types' in api_data:
                rooms_data = api_data.get('rooms', api_data.get('room_types', []))

                if isinstance(rooms_data, list):
                    for room in rooms_data:
                        if isinstance(room, dict):
                            room_info = {
                                'type': room.get('room_type', room.get('type', room.get('name', 'unknown'))),
                                'description': room.get('description', ''),
                                'capacity': room.get('capacity', room.get('max_occupancy', 0)),
                                'bed_type': room.get('bed_type', ''),
                                'size': room.get('size', ''),
                                'amenities_count': len(room.get('amenities', [])),
                                'available': room.get('available', True)
                            }
                            room_types.append(room_info)
                elif isinstance(rooms_data, dict):
                    for room_type, room_data in rooms_data.items():
                        if isinstance(room_data, dict):
                            room_info = {
                                'type': room_type,
                                'description': room_data.get('description', ''),
                                'capacity': room_data.get('capacity', room_data.get('max_occupancy', 0)),
                                'bed_type': room_data.get('bed_type', ''),
                                'size': room_data.get('size', ''),
                                'amenities_count': len(room_data.get('amenities', [])),
                                'available': room_data.get('available', True)
                            }
                            room_types.append(room_info)

        except Exception as e:
            logger.error(f"Error extracting room types: {str(e)}")

        return room_types

    def _create_amenity_summary(self, property_amenities: Dict, room_amenities: Dict) -> Dict[str, Any]:
        """
        Create a comprehensive amenity summary
        """
        summary = {
            'property_level': {},
            'room_level': {},
            'all_amenities': set(),
            'amenity_locations': {}  # Track where each amenity is available
        }

        try:
            # Process property amenities
            for category, amenities in property_amenities.items():
                if isinstance(amenities, list):
                    for amenity in amenities:
                        amenity_normalized = self._normalize_amenity(amenity)
                        summary['property_level'][amenity_normalized] = True
                        summary['all_amenities'].add(amenity_normalized)

                        if amenity_normalized not in summary['amenity_locations']:
                            summary['amenity_locations'][amenity_normalized] = []
                        summary['amenity_locations'][amenity_normalized].append('property')

            # Process room amenities
            for room_type, room_data in room_amenities.items():
                summary['room_level'][room_type] = {}

                if isinstance(room_data, dict):
                    for category, amenities in room_data.items():
                        if isinstance(amenities, list):
                            for amenity in amenities:
                                amenity_normalized = self._normalize_amenity(amenity)
                                summary['room_level'][room_type][amenity_normalized] = True
                                summary['all_amenities'].add(amenity_normalized)

                                if amenity_normalized not in summary['amenity_locations']:
                                    summary['amenity_locations'][amenity_normalized] = []
                                summary['amenity_locations'][amenity_normalized].append(f'room_{room_type}')

            # Convert set to list for JSON serialization
            summary['all_amenities'] = list(summary['all_amenities'])

        except Exception as e:
            logger.error(f"Error creating amenity summary: {str(e)}")

        return summary

    def _normalize_amenity(self, amenity: str) -> str:
        """
        Normalize amenity names for consistent comparison
        """
        if not isinstance(amenity, str):
            return str(amenity).lower().replace(' ', '_')

        amenity_lower = amenity.lower().strip()

        # Map common variations to standard names
        amenity_mappings = {
            'swimming pool': 'swimming_pool',
            'pool': 'swimming_pool',
            'wifi': 'wifi',
            'wi-fi': 'wifi',
            'internet': 'wifi',
            'air conditioning': 'ac',
            'ac': 'ac',
            'television': 'tv',
            'tv': 'tv',
            'parking': 'parking',
            'valet parking': 'valet_parking',
            'spa': 'spa',
            'gym': 'gym',
            'fitness center': 'gym',
            'restaurant': 'restaurant',
            'dining': 'restaurant',
            'breakfast': 'breakfast',
            'elevator': 'elevator',
            'lift': 'elevator',
            'laundry': 'laundry',
            'room service': 'room_service',
            'banquet': 'banquet',
            'conference room': 'conference_room',
            'meeting room': 'meeting_room'
        }

        return amenity_mappings.get(amenity_lower, amenity_lower.replace(' ', '_'))

    def _get_city_aliases(self) -> Dict[str, List[str]]:
        """Get comprehensive city alias mapping"""
        return {
            # Major Metro Cities
            'mumbai': ['Mumbai'],
            'bombay': ['Mumbai'],
            'bom': ['Mumbai'],

            'bangalore': ['Bangalore'],
            'bengaluru': ['Bangalore'],
            'blr': ['Bangalore'],

            'delhi': ['Delhi', 'New Delhi'],
            'new delhi': ['New Delhi', 'Delhi'],
            'gurgaon': ['Gurgaon'],
            'gurugram': ['Gurgaon'],
            'noida': ['Noida'],
            'faridabad': ['Faridabad'],

            'chennai': ['Chennai'],
            'madras': ['Chennai'],

            'kolkata': ['Kolkata'],
            'calcutta': ['Kolkata'],

            'hyderabad': ['Hyderabad'],
            'secunderabad': ['Hyderabad'],
            'cyberabad': ['Hyderabad'],

            'pune': ['Pune'],
            'poona': ['Pune'],

            # Other major cities with aliases
            'kochi': ['Kochi'],
            'cochin': ['Kochi'],
            'thiruvananthapuram': ['Thiruvananthapuram'],
            'trivandrum': ['Thiruvananthapuram'],
            'mysore': ['Mysore'],
            'mysuru': ['Mysore'],
            'mangalore': ['Mangalore'],
            'mangaluru': ['Mangalore'],
            'vadodara': ['Vadodara'],
            'baroda': ['Vadodara'],
            'visakhapatnam': ['Visakhapatnam'],
            'vizag': ['Visakhapatnam'],
            'coimbatore': ['Coimbatore'],
            'cbe': ['Coimbatore'],
            'pondicherry': ['Pondicherry'],
            'puducherry': ['Pondicherry'],
            'goa': ['Goa'],
            'panaji': ['Goa'],
            'margao': ['Goa']
        }

    def _has_amenities(self, hotel_details: Dict[str, Any], required_amenities: List[str]) -> bool:
        """
        Check if hotel has the required amenities
        """
        if not hotel_details or 'amenities' not in hotel_details:
            return False

        hotel_amenities = hotel_details['amenities']

        # Convert hotel amenities to lowercase text for matching
        amenity_text = str(hotel_amenities).lower()

        # Check each required amenity
        for required_amenity in required_amenities:
            if required_amenity in self.amenity_mapping:
                keywords = self.amenity_mapping[required_amenity]
                found = False

                for keyword in keywords:
                    if keyword.lower() in amenity_text:
                        found = True
                        break

                if not found:
                    return False
            else:
                # Direct keyword search if not in mapping
                if required_amenity.lower() not in amenity_text:
                    return False

        return True

    def get_hotels_by_city(self, city: str) -> List[Dict[str, Any]]:
        """
        Get all hotels in a specific city
        """
        return self.search_hotels(city=city)

    def get_hotels_with_amenity(self, amenity: str) -> List[Dict[str, Any]]:
        """
        Get all hotels with a specific amenity
        """
        return self.search_hotels(amenities=[amenity])

    def find_hotel_by_name(self, name: str, threshold: int = None) -> Optional[Dict[str, Any]]:
        """
        Find hotel by name using fuzzy matching
        """
        if threshold is None:
            threshold = Config.FUZZY_MATCH_THRESHOLD

        if self.hotels_df is None:
            self.load_hotel_data()

        hotel_names = self.hotels_df['name'].tolist()

        # Use fuzzy matching to find the best match
        best_match = process.extractOne(name, hotel_names, scorer=fuzz.ratio)

        if best_match and best_match[1] >= threshold:
            # Find the hotel with this name
            hotel_row = self.hotels_df[self.hotels_df['name'] == best_match[0]]
            if not hotel_row.empty:
                row = hotel_row.iloc[0]
                return {
                    'property_id': int(row['property_id']),
                    'name': row['name'],
                    'city_name': row['city_name'],
                    'locality_name': row['locality_name'],
                    'match_score': best_match[1]
                }

        return None

    def clear_stale_cache(self):
        """Clear all stale cache entries to ensure fresh data"""
        current_time = time.time()
        stale_keys = []

        for property_id, timestamp in self._cache_timestamps.items():
            if current_time - timestamp >= self._cache_ttl:
                stale_keys.append(property_id)

        for key in stale_keys:
            if key in self._api_cache:
                del self._api_cache[key]
            if key in self._cache_timestamps:
                del self._cache_timestamps[key]

        if stale_keys:
            logger.warning(f"🗑️ Cleared {len(stale_keys)} stale cache entries - NO STALE DATA ALLOWED")

    def force_clear_all_cache(self):
        """Force clear ALL cache to ensure absolutely fresh data"""
        cache_count = len(self._api_cache)
        self._api_cache.clear()
        self._cache_timestamps.clear()
        logger.warning(f"🧹 FORCE CLEARED ALL CACHE: {cache_count} entries removed - GUARANTEED FRESH DATA")

    def force_fresh_data(self, property_id: int) -> Dict[str, Any]:
        """Force fetch fresh data for a specific property (no stale data)"""
        # Remove from cache if exists
        if property_id in self._api_cache:
            del self._api_cache[property_id]
        if property_id in self._cache_timestamps:
            del self._cache_timestamps[property_id]

        logger.info(f"Forcing fresh data fetch for property {property_id}")
        # Fetch fresh data
        return self.get_hotel_details(property_id)

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics to monitor freshness"""
        current_time = time.time()
        fresh_count = 0
        stale_count = 0

        for timestamp in self._cache_timestamps.values():
            if current_time - timestamp < self._cache_ttl:
                fresh_count += 1
            else:
                stale_count += 1

        return {
            'total_cached': len(self._api_cache),
            'fresh_entries': fresh_count,
            'stale_entries': stale_count,
            'cache_ttl_seconds': self._cache_ttl,
            'no_stale_data_guarantee': True
        }