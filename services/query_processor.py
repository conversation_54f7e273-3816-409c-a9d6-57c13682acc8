import re
import logging
from typing import Dict, List, Any, Optional
from fuzzywuzzy import fuzz, process
from utils.nlp_utils import NLPUtils

logger = logging.getLogger(__name__)

class QueryProcessor:
    """
    Processes natural language queries about hotel portfolio
    """

    def __init__(self, hotel_service):
        self.hotel_service = hotel_service
        self.nlp_utils = NLPUtils()

        # Define amenity keywords and their mappings
        self.amenity_keywords = {
            'swimming_pool': ['swimming pool', 'pool', 'swimming', 'swim'],
            'spa': ['spa', 'massage', 'wellness', 'therapy'],
            'banquet': ['banquet', 'banquet hall', 'conference', 'meeting room', 'event hall'],
            'parking': ['parking', 'valet', 'car park', 'vehicle parking'],
            'breakfast': ['breakfast', 'morning meal', 'complimentary breakfast'],
            'elevator': ['elevator', 'lift', 'escalator'],
            'laundry': ['laundry', 'washing', 'dry cleaning'],
            'wifi': ['wifi', 'internet', 'wireless'],
            'gym': ['gym', 'fitness', 'exercise', 'workout'],
            'restaurant': ['restaurant', 'dining', 'food', 'cuisine'],
            'room_service': ['room service', 'in-room dining'],
            'ac': ['ac', 'air conditioning', 'air conditioner', 'cooling'],
            'tv': ['tv', 'television', 'cable tv'],
            'twin_bed': ['twin bed', 'twin beds', 'two beds', 'separate beds'],
            'double_bed': ['double bed', 'king bed', 'queen bed', 'large bed'],
            'balcony': ['balcony', 'terrace', 'outdoor space'],
            'view': ['view', 'scenic view', 'mountain view', 'city view', 'lake view']
        }

        # Query type patterns
        self.query_patterns = {
            'specific_hotel_amenity': [
                r'does\s+(.+?)\s+have\s+(.+)',
                r'(.+?)\s+has\s+(.+)',
                r'(.+?)\s+amenities',
                r'amenities\s+at\s+(.+)',
                r'facilities\s+at\s+(.+)'
            ],
            'city_amenity_search': [
                r'hotels?\s+with\s+(.+?)\s+in\s+(.+)',
                r'(.+?)\s+in\s+(.+?)\s+hotels?',
                r'which\s+hotels?\s+have\s+(.+?)\s+in\s+(.+)',
                r'(.+?)\s+available\s+in\s+(.+)',
                r'where\s+is\s+(.+?)\s+available\s+in\s+(.+)'
            ],
            'general_amenity_search': [
                r'hotels?\s+with\s+(.+)',
                r'which\s+hotels?\s+have\s+(.+)',
                r'(.+?)\s+hotels?',
                r'find\s+hotels?\s+with\s+(.+)'
            ],
            'city_search': [
                r'hotels?\s+in\s+(.+)',
                r'(.+?)\s+hotels?',
                r'properties\s+in\s+(.+)'
            ]
        }

    def process_query(self, query: str) -> Dict[str, Any]:
        """
        Main method to process natural language queries
        """
        query = query.lower().strip()
        logger.info(f"Processing query: {query}")

        # Determine query type and extract information
        query_info = self._analyze_query(query)

        # Process based on query type
        if query_info['type'] == 'specific_hotel_amenity':
            return self._handle_specific_hotel_query(query_info)
        elif query_info['type'] == 'city_amenity_search':
            return self._handle_city_amenity_query(query_info)
        elif query_info['type'] == 'general_amenity_search':
            return self._handle_general_amenity_query(query_info)
        elif query_info['type'] == 'city_search':
            return self._handle_city_search_query(query_info)
        else:
            return self._handle_general_query(query)

    def _analyze_query(self, query: str) -> Dict[str, Any]:
        """
        Analyze the query to determine type and extract key information
        """
        query_info = {
            'type': 'unknown',
            'hotel_name': None,
            'city': None,
            'amenities': [],
            'raw_query': query
        }

        # Check for specific hotel amenity queries
        for pattern in self.query_patterns['specific_hotel_amenity']:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                query_info['type'] = 'specific_hotel_amenity'
                if len(match.groups()) >= 2:
                    query_info['hotel_name'] = match.group(1).strip()
                    amenity_text = match.group(2).strip()
                    query_info['amenities'] = self._extract_amenities(amenity_text)
                elif len(match.groups()) == 1:
                    query_info['hotel_name'] = match.group(1).strip()
                break

        # Check for city + amenity queries
        if query_info['type'] == 'unknown':
            for pattern in self.query_patterns['city_amenity_search']:
                match = re.search(pattern, query, re.IGNORECASE)
                if match:
                    query_info['type'] = 'city_amenity_search'
                    if len(match.groups()) >= 2:
                        amenity_text = match.group(1).strip()
                        city_text = match.group(2).strip()
                        query_info['amenities'] = self._extract_amenities(amenity_text)
                        query_info['city'] = self._extract_city(city_text)
                    break

        # Check for general amenity queries
        if query_info['type'] == 'unknown':
            for pattern in self.query_patterns['general_amenity_search']:
                match = re.search(pattern, query, re.IGNORECASE)
                if match:
                    query_info['type'] = 'general_amenity_search'
                    amenity_text = match.group(1).strip()
                    query_info['amenities'] = self._extract_amenities(amenity_text)
                    break

        # Check for city search queries
        if query_info['type'] == 'unknown':
            for pattern in self.query_patterns['city_search']:
                match = re.search(pattern, query, re.IGNORECASE)
                if match:
                    query_info['type'] = 'city_search'
                    city_text = match.group(1).strip()
                    query_info['city'] = self._extract_city(city_text)
                    break

        # Extract any remaining cities and amenities from the full query
        if not query_info['city']:
            query_info['city'] = self._extract_city(query)

        if not query_info['amenities']:
            query_info['amenities'] = self._extract_amenities(query)

        logger.info(f"Query analysis result: {query_info}")
        return query_info

    def _extract_amenities(self, text: str) -> List[str]:
        """
        Extract amenities from text using keyword matching
        """
        found_amenities = []
        text = text.lower()

        for amenity, keywords in self.amenity_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    found_amenities.append(amenity)
                    break

        return list(set(found_amenities))  # Remove duplicates

    def _extract_city(self, text: str) -> Optional[str]:
        """
        Extract city name from text using fuzzy matching with known cities
        """
        text = text.lower().strip()

        # Get all known cities from hotel data
        known_cities = self.hotel_service.get_all_cities()

        # Try exact match first
        for city in known_cities:
            if city.lower() in text or text in city.lower():
                return city

        # Try fuzzy matching
        best_match = process.extractOne(text, known_cities, scorer=fuzz.partial_ratio)
        if best_match and best_match[1] > 70:  # 70% similarity threshold
            return best_match[0]

        # Try to extract city names from common patterns
        city_patterns = [
            r'in\s+([a-zA-Z\s]+)',
            r'at\s+([a-zA-Z\s]+)',
            r'near\s+([a-zA-Z\s]+)'
        ]

        for pattern in city_patterns:
            match = re.search(pattern, text)
            if match:
                potential_city = match.group(1).strip()
                best_match = process.extractOne(potential_city, known_cities, scorer=fuzz.ratio)
                if best_match and best_match[1] > 60:
                    return best_match[0]

        return None

    def _handle_specific_hotel_query(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle queries about specific hotels
        """
        hotel_name = query_info['hotel_name']

        # Find the hotel using fuzzy matching
        hotel = self._find_hotel_by_name(hotel_name)

        if not hotel:
            return {
                'type': 'hotel_not_found',
                'message': f"Sorry, I couldn't find a hotel matching '{hotel_name}'. Please check the spelling or try a different name.",
                'suggestions': self._get_hotel_name_suggestions(hotel_name)
            }

        # Get detailed hotel information
        hotel_details = self.hotel_service.get_hotel_details(hotel['property_id'])

        if query_info['amenities']:
            # Check specific amenities
            amenity_status = {}
            for amenity in query_info['amenities']:
                amenity_status[amenity] = self._check_hotel_amenity(hotel_details, amenity)

            return {
                'type': 'specific_hotel_amenities',
                'hotel': hotel,
                'amenity_status': amenity_status,
                'message': self._format_amenity_response(hotel['name'], amenity_status)
            }
        else:
            # Return all amenities for the hotel
            return {
                'type': 'hotel_details',
                'hotel': hotel,
                'details': hotel_details,
                'message': f"Here are the details for {hotel['name']}"
            }

    def _handle_city_amenity_query(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle queries about hotels with specific amenities in a city
        """
        city = query_info['city']
        amenities = query_info['amenities']

        if not city:
            return {
                'type': 'city_not_found',
                'message': "I couldn't identify the city in your query. Please specify a city name.",
                'available_cities': self.hotel_service.get_all_cities()[:20]  # Show first 20 cities
            }

        # Search for hotels
        results = self.hotel_service.search_hotels(city=city, amenities=amenities)

        return {
            'type': 'city_amenity_search',
            'city': city,
            'amenities': amenities,
            'results': results,
            'count': len(results),
            'message': self._format_search_results_message(city, amenities, len(results))
        }

    def _handle_general_amenity_query(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle general amenity queries across all hotels
        """
        amenities = query_info['amenities']

        if not amenities:
            return {
                'type': 'no_amenities_found',
                'message': "I couldn't identify specific amenities in your query. Please mention amenities like 'swimming pool', 'spa', 'banquet hall', etc."
            }

        # Search across all hotels
        results = self.hotel_service.search_hotels(amenities=amenities)

        return {
            'type': 'general_amenity_search',
            'amenities': amenities,
            'results': results,
            'count': len(results),
            'message': self._format_general_amenity_message(amenities, len(results))
        }

    def _handle_city_search_query(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle queries about all hotels in a city
        """
        city = query_info['city']

        if not city:
            return {
                'type': 'city_not_found',
                'message': "Please specify a city name to search for hotels."
            }

        results = self.hotel_service.search_hotels(city=city)

        return {
            'type': 'city_search',
            'city': city,
            'results': results,
            'count': len(results),
            'message': f"Found {len(results)} hotels in {city}"
        }

    def _handle_general_query(self, query: str) -> Dict[str, Any]:
        """
        Handle general queries that don't match specific patterns
        """
        return {
            'type': 'general_help',
            'message': """I can help you find hotels and their amenities. Here are some example queries:

• "Which hotel has banquet in Nagpur?"
• "Hotels with swimming pool in Pondicherry"
• "Does Treebo Paradise have spa facilities?"
• "Twin bed availability in Indore"
• "All hotels in Bangalore"

Please try rephrasing your question using these patterns.""",
            'examples': [
                "Hotels with swimming pool in Mumbai",
                "Does Treebo Amber International have parking?",
                "Banquet facilities in Delhi",
                "All hotels in Goa"
            ]
        }

    def _find_hotel_by_name(self, hotel_name: str) -> Optional[Dict[str, Any]]:
        """
        Find hotel by name using fuzzy matching
        """
        all_hotels = self.hotel_service.get_all_hotels()
        hotel_names = [hotel['name'] for hotel in all_hotels]

        # Try fuzzy matching
        best_match = process.extractOne(hotel_name, hotel_names, scorer=fuzz.ratio)

        if best_match and best_match[1] > Config.FUZZY_MATCH_THRESHOLD:  # Use configurable threshold
            # Find the hotel object
            for hotel in all_hotels:
                if hotel['name'] == best_match[0]:
                    return hotel

        return None

    def _get_hotel_name_suggestions(self, hotel_name: str) -> List[str]:
        """
        Get hotel name suggestions for misspelled names
        """
        all_hotels = self.hotel_service.get_all_hotels()
        hotel_names = [hotel['name'] for hotel in all_hotels]

        # Get top 5 similar names
        suggestions = process.extract(hotel_name, hotel_names, limit=5, scorer=fuzz.ratio)
        return [suggestion[0] for suggestion in suggestions if suggestion[1] > Config.LOW_CONFIDENCE_THRESHOLD * 100]

    def _check_hotel_amenity(self, hotel_details: Dict[str, Any], amenity: str) -> bool:
        """
        Check if a hotel has a specific amenity
        """
        # This would check the API response for amenity information
        # For now, return a placeholder
        return True  # This should be implemented based on the API response structure

    def _format_amenity_response(self, hotel_name: str, amenity_status: Dict[str, bool]) -> str:
        """
        Format the response for amenity queries
        """
        available = [amenity for amenity, status in amenity_status.items() if status]
        not_available = [amenity for amenity, status in amenity_status.items() if not status]

        message = f"{hotel_name} "

        if available:
            message += f"has: {', '.join(available)}"

        if not_available:
            if available:
                message += f" but does not have: {', '.join(not_available)}"
            else:
                message += f"does not have: {', '.join(not_available)}"

        return message

    def _format_search_results_message(self, city: str, amenities: List[str], count: int) -> str:
        """
        Format message for search results
        """
        amenity_text = ', '.join(amenities) if amenities else 'the requested amenities'
        return f"Found {count} hotels in {city} with {amenity_text}"

    def _format_general_amenity_message(self, amenities: List[str], count: int) -> str:
        """
        Format message for general amenity search
        """
        amenity_text = ', '.join(amenities)
        return f"Found {count} hotels with {amenity_text}"
