"""
Brand Standards Service for managing guaranteed amenities across the portfolio
"""

import logging
from typing import Dict, List, Optional, Set, Any
from psycopg2.extras import RealDictCursor
import json
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class BrandStandardsService:
    def __init__(self, db_connection):
        self.connection = db_connection
        self.brand_standards = {}
        self.guaranteed_amenities = {}
        self.amenity_categories = {}
        self.context_cache = {}
        self.cache_file = 'brand_standards_cache.json'
        self.load_brand_standards()
        self.load_cached_context()

    def load_brand_standards(self):
        """Load brand standards from database"""
        try:
            query = """
            SELECT brand_name, amenity_key, amenity_name, description,
                   category, guaranteed, created_at, updated_at
            FROM brand_standards
            WHERE guaranteed = TRUE
            ORDER BY brand_name, category, amenity_key
            """

            with self.connection.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(query)
                results = cursor.fetchall()

                for row in results:
                    brand = row['brand_name']
                    amenity_key = row['amenity_key']

                    # Store by brand
                    if brand not in self.brand_standards:
                        self.brand_standards[brand] = {}

                    self.brand_standards[brand][amenity_key] = {
                        'name': row['amenity_name'],
                        'description': row['description'],
                        'category': row['category'],
                        'guaranteed': row['guaranteed']
                    }

                    # Store guaranteed amenities globally
                    if brand not in self.guaranteed_amenities:
                        self.guaranteed_amenities[brand] = set()
                    self.guaranteed_amenities[brand].add(amenity_key)

                    # Store category mapping
                    self.amenity_categories[amenity_key] = row['category']

                logger.info(f"Loaded brand standards for {len(self.brand_standards)} brands")
                for brand, amenities in self.guaranteed_amenities.items():
                    logger.info(f"  {brand}: {len(amenities)} guaranteed amenities")

        except Exception as e:
            logger.error(f"Error loading brand standards: {str(e)}")
            self.brand_standards = {}
            self.guaranteed_amenities = {}

    def is_guaranteed_amenity(self, amenity_key: str, brand: str = 'treebo') -> bool:
        """Check if an amenity is guaranteed for a brand"""
        return amenity_key in self.guaranteed_amenities.get(brand, set())

    def get_guaranteed_amenities(self, brand: str = 'treebo') -> Dict[str, Any]:
        """Get all guaranteed amenities for a brand"""
        if brand not in self.brand_standards:
            return {}

        guaranteed = {}
        for amenity_key, details in self.brand_standards[brand].items():
            guaranteed[amenity_key] = details

        return guaranteed

    def enhance_amenity_summary(self, amenity_summary: Dict[str, Any], brand: str = 'treebo') -> Dict[str, Any]:
        """Enhance amenity summary with guaranteed brand standards"""
        enhanced_summary = amenity_summary.copy()

        # Get guaranteed amenities for the brand
        guaranteed = self.get_guaranteed_amenities(brand)

        # Ensure property amenities include guaranteed ones
        if 'property_amenities' not in enhanced_summary:
            enhanced_summary['property_amenities'] = []

        property_amenities = enhanced_summary['property_amenities']
        existing_amenity_keys = {amenity.get('amenity_key') for amenity in property_amenities if isinstance(amenity, dict)}

        # Add missing guaranteed amenities
        for amenity_key, details in guaranteed.items():
            if amenity_key not in existing_amenity_keys:
                property_amenities.append({
                    'amenity_key': amenity_key,
                    'amenity_name': details['name'],
                    'category': details['category'],
                    'guaranteed': True,
                    'source': 'brand_standards'
                })
                logger.info(f"Added guaranteed amenity '{amenity_key}' to property amenities")

        # Mark existing amenities as guaranteed if they are
        for amenity in property_amenities:
            if isinstance(amenity, dict) and amenity.get('amenity_key'):
                amenity_key = amenity['amenity_key']
                if self.is_guaranteed_amenity(amenity_key, brand):
                    amenity['guaranteed'] = True
                    amenity['brand_standard'] = True

        return enhanced_summary

    def process_hotel_amenities(self, hotel_data: Dict[str, Any], brand: str = 'treebo') -> Dict[str, Any]:
        """Process hotel amenities with brand standards integration"""
        processed_data = hotel_data.copy()

        # Extract amenity summary
        amenity_summary = hotel_data.get('amenity_summary', {})

        if amenity_summary:
            # Enhance with brand standards
            enhanced_summary = self.enhance_amenity_summary(amenity_summary, brand)
            processed_data['amenity_summary'] = enhanced_summary

            # Process property amenities
            property_amenities = {}
            for amenity in enhanced_summary.get('property_amenities', []):
                if isinstance(amenity, dict) and 'amenity_key' in amenity:
                    amenity_key = amenity['amenity_key']
                    property_amenities[amenity_key] = {
                        'name': amenity.get('amenity_name', amenity_key),
                        'category': amenity.get('category', 'other'),
                        'guaranteed': amenity.get('guaranteed', False),
                        'brand_standard': amenity.get('brand_standard', False)
                    }

            processed_data['property_amenities'] = property_amenities

            # Process room amenities by room type
            room_amenities = {}
            room_types = ['ACACIA', 'MAPLE', 'OAK', 'MAHOGANY']

            for room_type in room_types:
                room_amenity_list = enhanced_summary.get(room_type, [])
                if room_amenity_list:
                    room_amenities[room_type.lower()] = {}
                    for amenity in room_amenity_list:
                        if isinstance(amenity, dict) and 'amenity_key' in amenity:
                            amenity_key = amenity['amenity_key']
                            room_amenities[room_type.lower()][amenity_key] = {
                                'name': amenity.get('amenity_name', amenity_key),
                                'category': amenity.get('category', 'room'),
                                'available': True
                            }

            processed_data['room_amenities'] = room_amenities

        else:
            # If no amenity summary, provide guaranteed amenities
            guaranteed = self.get_guaranteed_amenities(brand)
            processed_data['property_amenities'] = {
                key: {
                    'name': details['name'],
                    'category': details['category'],
                    'guaranteed': True,
                    'brand_standard': True
                }
                for key, details in guaranteed.items()
            }
            processed_data['room_amenities'] = {}

        return processed_data

    def save_context_cache(self):
        """Save context to cache file for persistence"""
        try:
            cache_data = {
                'brand_standards': self.brand_standards,
                'guaranteed_amenities': {
                    brand: list(amenities) for brand, amenities in self.guaranteed_amenities.items()
                },
                'amenity_categories': self.amenity_categories,
                'timestamp': str(datetime.now())
            }

            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2, default=str)

            logger.info(f"Brand standards context saved to {self.cache_file}")

        except Exception as e:
            logger.error(f"Error saving context cache: {str(e)}")

    def load_cached_context(self):
        """Load context from cache file"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)

                # Restore guaranteed amenities as sets
                if 'guaranteed_amenities' in cache_data:
                    for brand, amenities in cache_data['guaranteed_amenities'].items():
                        self.guaranteed_amenities[brand] = set(amenities)

                logger.info(f"Brand standards context loaded from cache")

        except Exception as e:
            logger.warning(f"Could not load cached context: {str(e)}")

    def get_amenity_statistics(self) -> Dict[str, Any]:
        """Get statistics about brand standards"""
        stats = {
            'total_brands': len(self.brand_standards),
            'total_guaranteed_amenities': sum(len(amenities) for amenities in self.guaranteed_amenities.values()),
            'amenities_by_brand': {},
            'amenities_by_category': {}
        }

        # Count by brand
        for brand, amenities in self.guaranteed_amenities.items():
            stats['amenities_by_brand'][brand] = len(amenities)

        # Count by category
        for amenity_key, category in self.amenity_categories.items():
            if category not in stats['amenities_by_category']:
                stats['amenities_by_category'][category] = 0
            stats['amenities_by_category'][category] += 1

        return stats
