"""
Context Persistence Service for maintaining application context across sessions
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import pickle

logger = logging.getLogger(__name__)

class ContextPersistenceService:
    def __init__(self, cache_dir: str = 'cache'):
        self.cache_dir = cache_dir
        self.context_file = os.path.join(cache_dir, 'app_context.json')
        self.binary_cache_file = os.path.join(cache_dir, 'app_context.pkl')
        self.max_age_hours = 24  # Context expires after 24 hours
        
        # Ensure cache directory exists
        os.makedirs(cache_dir, exist_ok=True)
        
        # Initialize context
        self.context = self.load_context()
    
    def save_context(self, context_data: Dict[str, Any]) -> bool:
        """Save context data to persistent storage"""
        try:
            # Add metadata
            context_with_metadata = {
                'data': context_data,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'app_name': 'hotel_search_system'
            }
            
            # Save as JSON (human readable)
            with open(self.context_file, 'w') as f:
                json.dump(context_with_metadata, f, indent=2, default=str)
            
            # Save as pickle (for complex objects)
            with open(self.binary_cache_file, 'wb') as f:
                pickle.dump(context_with_metadata, f)
            
            logger.info(f"Context saved successfully to {self.context_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving context: {str(e)}")
            return False
    
    def load_context(self) -> Dict[str, Any]:
        """Load context data from persistent storage"""
        try:
            # Try loading from JSON first
            if os.path.exists(self.context_file):
                with open(self.context_file, 'r') as f:
                    context_with_metadata = json.load(f)
                
                # Check if context is still valid
                if self._is_context_valid(context_with_metadata):
                    logger.info("Context loaded successfully from JSON")
                    return context_with_metadata.get('data', {})
                else:
                    logger.warning("Context expired, starting fresh")
                    return {}
            
            # Try loading from pickle as fallback
            elif os.path.exists(self.binary_cache_file):
                with open(self.binary_cache_file, 'rb') as f:
                    context_with_metadata = pickle.load(f)
                
                if self._is_context_valid(context_with_metadata):
                    logger.info("Context loaded successfully from pickle")
                    return context_with_metadata.get('data', {})
                else:
                    logger.warning("Binary context expired, starting fresh")
                    return {}
            
            else:
                logger.info("No existing context found, starting fresh")
                return {}
                
        except Exception as e:
            logger.error(f"Error loading context: {str(e)}")
            return {}
    
    def _is_context_valid(self, context_with_metadata: Dict[str, Any]) -> bool:
        """Check if context is still valid (not expired)"""
        try:
            timestamp_str = context_with_metadata.get('timestamp')
            if not timestamp_str:
                return False
            
            timestamp = datetime.fromisoformat(timestamp_str)
            age = datetime.now() - timestamp
            
            return age < timedelta(hours=self.max_age_hours)
            
        except Exception as e:
            logger.error(f"Error checking context validity: {str(e)}")
            return False
    
    def update_context(self, key: str, value: Any) -> bool:
        """Update a specific key in the context"""
        try:
            self.context[key] = value
            return self.save_context(self.context)
        except Exception as e:
            logger.error(f"Error updating context key '{key}': {str(e)}")
            return False
    
    def get_context_value(self, key: str, default: Any = None) -> Any:
        """Get a specific value from context"""
        return self.context.get(key, default)
    
    def get_full_context(self) -> Dict[str, Any]:
        """Get the full context dictionary"""
        return self.context.copy()
    
    def clear_context(self) -> bool:
        """Clear all context data"""
        try:
            self.context = {}
            
            # Remove files
            if os.path.exists(self.context_file):
                os.remove(self.context_file)
            if os.path.exists(self.binary_cache_file):
                os.remove(self.binary_cache_file)
            
            logger.info("Context cleared successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing context: {str(e)}")
            return False
    
    def save_brand_standards_context(self, brand_standards_data: Dict[str, Any]) -> bool:
        """Save brand standards specific context"""
        brand_context = {
            'brand_standards': brand_standards_data,
            'last_updated': datetime.now().isoformat(),
            'total_brands': len(brand_standards_data.get('brand_standards', {})),
            'guaranteed_amenities_count': sum(
                len(amenities) for amenities in brand_standards_data.get('guaranteed_amenities', {}).values()
            )
        }
        
        return self.update_context('brand_standards_context', brand_context)
    
    def save_city_alias_context(self, city_alias_data: Dict[str, Any]) -> bool:
        """Save city alias specific context"""
        city_context = {
            'city_aliases': city_alias_data,
            'last_updated': datetime.now().isoformat(),
            'total_cities': len(city_alias_data.get('all_cities', set())),
            'total_aliases': len(city_alias_data.get('city_aliases', {}))
        }
        
        return self.update_context('city_alias_context', city_context)
    
    def save_hotel_metadata_context(self, hotel_metadata_data: Dict[str, Any]) -> bool:
        """Save hotel metadata specific context"""
        hotel_context = {
            'hotel_metadata': hotel_metadata_data,
            'last_updated': datetime.now().isoformat(),
            'total_hotels': hotel_metadata_data.get('total_hotels', 0),
            'total_cities': hotel_metadata_data.get('total_cities', 0)
        }
        
        return self.update_context('hotel_metadata_context', hotel_context)
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of the current context"""
        summary = {
            'context_loaded': bool(self.context),
            'total_keys': len(self.context),
            'last_updated': None,
            'components': {}
        }
        
        # Check each component
        for component in ['brand_standards_context', 'city_alias_context', 'hotel_metadata_context']:
            component_data = self.context.get(component, {})
            if component_data:
                summary['components'][component] = {
                    'loaded': True,
                    'last_updated': component_data.get('last_updated'),
                    'data_points': len(component_data) - 1  # Exclude last_updated from count
                }
            else:
                summary['components'][component] = {'loaded': False}
        
        return summary
    
    def refresh_context_if_needed(self) -> bool:
        """Refresh context if it's expired or missing"""
        try:
            # Check if we need to refresh
            if not self.context:
                logger.info("Context is empty, needs refresh")
                return True
            
            # Check age of each component
            for component in ['brand_standards_context', 'city_alias_context', 'hotel_metadata_context']:
                component_data = self.context.get(component, {})
                if component_data and 'last_updated' in component_data:
                    last_updated = datetime.fromisoformat(component_data['last_updated'])
                    age = datetime.now() - last_updated
                    if age > timedelta(hours=self.max_age_hours):
                        logger.info(f"Component {component} is expired, needs refresh")
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking if context needs refresh: {str(e)}")
            return True  # Refresh on error to be safe
