import json
import logging
from typing import Dict, List, Any, Optional
import openai
from openai import OpenAI
import re
from config import Config
from .city_alias_service import CityAliasService
from .hotel_metadata_service import HotelMetadataService
from .amenity_verification_service import AmenityVerificationService
from .geographic_intelligence_service import GeographicIntelligenceService

logger = logging.getLogger(__name__)

class AIQueryProcessor:
    """
    AI-powered query processor using OpenAI's latest API for intelligent query understanding
    """

    def __init__(self, hotel_service, db_service):
        self.hotel_service = hotel_service
        self.db_service = db_service

        # Initialize enhanced services
        try:
            self.city_alias_service = CityAliasService()
            self.hotel_metadata_service = HotelMetadataService()

            # Initialize geographic intelligence service first
            self.geographic_intelligence_service = GeographicIntelligenceService()

            # Initialize amenity verification service with geographic intelligence
            brand_standards_service = None
            if hotel_service and hasattr(hotel_service, 'brand_standards_service'):
                brand_standards_service = hotel_service.brand_standards_service

            self.amenity_verification_service = AmenityVerificationService(
                hotel_service, brand_standards_service, self.geographic_intelligence_service
            )

            logger.info("Enhanced services initialized successfully (including amenity verification and geographic intelligence)")
        except Exception as e:
            logger.error(f"Failed to initialize enhanced services: {str(e)}")
            self.city_alias_service = None
            self.hotel_metadata_service = None
            self.amenity_verification_service = None
            self.geographic_intelligence_service = None

        # Initialize OpenAI client
        if Config.OPENAI_API_KEY:
            self.client = OpenAI(api_key=Config.OPENAI_API_KEY)
        else:
            logger.warning("OpenAI API key not found. AI features will be limited.")
            self.client = None

        # Define the system prompt for hotel queries
        self.system_prompt = """
You are an intelligent hotel search assistant for a hotel portfolio management system. Your job is to understand natural language queries about hotels and convert them into structured search parameters.

Available hotel data includes:
- Property details (name, location, city, locality, micro market)
- Amenities (swimming pool, spa, banquet hall, parking, breakfast, elevator, laundry, wifi, gym, restaurant, room service, AC, TV, balcony, etc.)
- Room types (twin bed, double bed, single bed, king bed, queen bed, deluxe room, standard room, suite)
- Location hierarchy (state, city, locality, micro market)

Your response should be a JSON object with the following structure:
{
    "intent": "search_hotels|specific_hotel_info|amenity_check|location_search|comparison",
    "entities": {
        "hotels": ["hotel names if mentioned"],
        "cities": ["city names"],
        "states": ["state names"],
        "localities": ["locality names"],
        "amenities": ["amenity names"],
        "room_types": ["room type names"],
        "price_range": {"min": number, "max": number},
        "dates": {"check_in": "YYYY-MM-DD", "check_out": "YYYY-MM-DD"}
    },
    "filters": {
        "must_have": ["required amenities/features"],
        "nice_to_have": ["preferred amenities/features"],
        "exclude": ["amenities/features to exclude"]
    },
    "query_type": "simple|complex|comparison|specific",
    "confidence": 0.0-1.0,
    "clarification_needed": ["list of unclear aspects"],
    "suggested_response": "natural language response to user"
}

Examples:
- "Hotels with swimming pool in Mumbai" -> search for hotels in Mumbai with swimming pool amenity
- "Does Treebo Paradise have spa?" -> check specific hotel for spa amenity
- "Best hotels in Bangalore with parking and breakfast" -> search Bangalore hotels with both amenities
- "Twin bed rooms in Pune under 3000" -> search for specific room type with price filter
"""

    def process_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process natural language query using AI and return structured results
        """
        try:
            logger.info(f"Processing query: {query}")

            # FORCE CLEAR CACHE for geographic queries to prevent stale data
            if any(city_word in query.lower() for city_word in ['in ', 'kochi', 'mumbai', 'bangalore', 'delhi', 'chennai', 'pune', 'goa']):
                if self.hotel_service and hasattr(self.hotel_service, 'force_clear_all_cache'):
                    self.hotel_service.force_clear_all_cache()
                    logger.warning(f"🧹 FORCED CACHE CLEAR for geographic query: '{query}' - NO STALE DATA ALLOWED")

            # First, try AI-powered processing if available with timeout
            try:
                if self.client:
                    logger.info("Attempting AI analysis...")
                    ai_analysis = self._analyze_query_with_ai(query, context)
                    logger.info(f"AI analysis completed: {ai_analysis.get('intent', 'unknown')}")
                else:
                    logger.info("No OpenAI client, using fallback analysis")
                    ai_analysis = self._fallback_analysis(query)
            except Exception as ai_error:
                logger.warning(f"AI analysis failed, using fallback: {str(ai_error)}")
                ai_analysis = self._fallback_analysis(query)

            # Get available data context
            data_context = self._get_data_context()
            logger.info(f"Data context: {data_context.get('total_hotels', 0)} hotels available")

            # Execute the search based on AI analysis
            search_results = self._execute_search(ai_analysis, data_context)
            logger.info(f"Search executed, found {len(search_results)} initial results")

            # Enhance results with API data (only for amenity queries)
            if ai_analysis.get('filters', {}).get('must_have'):
                logger.info("Enhancing results with API data for amenity filtering...")
                enhanced_results = self._enhance_with_api_data(search_results, ai_analysis)
                logger.info(f"After API enhancement: {len(enhanced_results)} results")
            else:
                enhanced_results = search_results

            # Generate intelligent response
            response = self._generate_response(ai_analysis, enhanced_results, query)
            logger.info(f"Response generated with {response.get('total_results', 0)} results")

            return response

        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return self._error_response(query, str(e))

    def _analyze_query_with_ai(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Use OpenAI to analyze and understand the query
        """
        try:
            # Prepare context information
            context_info = ""
            if context:
                context_info = f"\nContext: {json.dumps(context, indent=2)}"

            # Get available cities and hotels for better context
            available_cities = self.db_service.get_all_cities()
            sample_hotels = self.db_service.get_sample_hotels(limit=10)

            enhanced_prompt = f"""
{self.system_prompt}

Available cities in our portfolio: {', '.join(available_cities[:20])}
Sample hotels: {', '.join([h['name'] for h in sample_hotels])}

{context_info}

User Query: "{query}"

Please analyze this query and provide the structured JSON response.
"""

            # Add timeout to OpenAI API call
            import signal

            def timeout_handler(signum, frame):
                raise TimeoutError("OpenAI API call timed out")

            # Set timeout for 10 seconds
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(10)

            try:
                response = self.client.chat.completions.create(
                    model=Config.OPENAI_MODEL,
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": enhanced_prompt}
                    ],
                    max_tokens=Config.OPENAI_MAX_TOKENS,
                    temperature=0.3
                )
            finally:
                signal.alarm(0)  # Cancel the alarm

            # Parse the AI response
            ai_response = response.choices[0].message.content

            # Try to extract JSON from the response
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                analysis = json.loads(json_match.group())
            else:
                # Fallback if JSON parsing fails
                analysis = self._fallback_analysis(query)
                analysis['ai_response'] = ai_response

            analysis['ai_powered'] = True
            return analysis

        except Exception as e:
            logger.error(f"AI analysis failed: {str(e)}")
            return self._fallback_analysis(query)

    def _fallback_analysis(self, query: str) -> Dict[str, Any]:
        """
        Enhanced fallback analysis when AI is not available
        """
        query_lower = query.lower()

        # Enhanced entity extraction using new services
        entities = {
            "hotels": self._extract_hotel_names(query),
            "cities": self._extract_cities(query),
            "amenities": self._extract_amenities(query),
            "room_types": self._extract_room_types(query)
        }

        # Store original query for intelligent suggestions
        entities["original_query"] = query

        # Determine intent
        intent = "search_hotels"
        if any(word in query_lower for word in ["does", "has", "have"]):
            intent = "amenity_check"
        elif "compare" in query_lower or "vs" in query_lower:
            intent = "comparison"

        # Log the extracted entities for debugging
        logger.info(f"Fallback analysis - Query: '{query}'")
        logger.info(f"Extracted entities - Cities: {entities['cities']}, Amenities: {entities['amenities']}")

        return {
            "intent": intent,
            "entities": entities,
            "filters": {
                "must_have": entities.get("amenities", []),
                "nice_to_have": [],
                "exclude": []
            },
            "query_type": "simple",
            "confidence": 0.6,
            "clarification_needed": [],
            "ai_powered": False
        }

    def _get_data_context(self) -> Dict[str, Any]:
        """
        Get context about available data
        """
        return {
            "total_hotels": self.db_service.get_hotel_count(),
            "cities": self.db_service.get_all_cities(),
            "available_amenities": self.db_service.get_available_amenities(),
            "room_types": self.db_service.get_available_room_types()
        }

    def _execute_search(self, analysis: Dict[str, Any], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Enhanced search execution using hotel metadata service
        """
        try:
            entities = analysis.get("entities", {})
            filters = analysis.get("filters", {})

            # Start with all possible results
            candidate_hotels = []

            # Use hotel metadata service for intelligent filtering
            if self.hotel_metadata_service:
                # Search by cities first
                if entities.get("cities"):
                    for city in entities["cities"]:
                        city_hotels = self.hotel_metadata_service.get_hotels_by_city(city)
                        candidate_hotels.extend(city_hotels)
                        logger.info(f"Found {len(city_hotels)} hotels in {city}")

                # Search by hotel names if specified
                if entities.get("hotels"):
                    for hotel_name in entities["hotels"]:
                        name_matches = self.hotel_metadata_service.search_hotels_by_name(hotel_name)
                        candidate_hotels.extend(name_matches)
                        logger.info(f"Found {len(name_matches)} hotels matching name '{hotel_name}'")

                # Search by localities if specified
                if entities.get("localities"):
                    for locality in entities["localities"]:
                        city = entities.get("cities", [None])[0]  # Use first city if available
                        locality_hotels = self.hotel_metadata_service.search_hotels_by_locality(locality, city)
                        candidate_hotels.extend(locality_hotels)
                        logger.info(f"Found {len(locality_hotels)} hotels in locality '{locality}'")

                # If no specific criteria, be very careful with suggestions
                if not candidate_hotels and not entities.get("cities") and not entities.get("hotels"):
                    # Only use intelligent suggestions if the query doesn't contain geographic terms
                    query_text = entities.get("original_query", "").lower()

                    # Check if query contains geographic terms that we couldn't resolve
                    geographic_terms = ['himachal', 'pradesh', 'state', 'mountain', 'hill', 'valley']
                    has_unresolved_geography = any(term in query_text for term in geographic_terms)

                    if not has_unresolved_geography:
                        # Safe to use intelligent suggestions
                        suggestions = self.hotel_metadata_service.get_advanced_hotel_suggestions(query_text)

                        # Combine all suggestion types
                        for suggestion_type in ['by_name', 'by_locality', 'by_location', 'by_city']:
                            candidate_hotels.extend(suggestions.get(suggestion_type, []))

                        logger.info(f"Found {len(candidate_hotels)} hotels from intelligent suggestions")
                    else:
                        logger.warning(f"Query contains unresolved geographic terms: {query_text}. Skipping intelligent suggestions.")

            # CRITICAL FALLBACK: Direct database query if no results
            if not candidate_hotels and self.db_service:
                logger.warning("🚨 CRITICAL FALLBACK: No hotels found, trying direct database query")

                # Try direct city-based search first
                if entities.get("cities"):
                    for city in entities["cities"]:
                        city_hotels = self.db_service.get_hotels_by_city(city)
                        candidate_hotels.extend(city_hotels)
                        logger.warning(f"🔧 DIRECT DB: Found {len(city_hotels)} hotels in {city}")

                # If still no results, try broader search
                if not candidate_hotels:
                    # Build search parameters for database service
                    search_params = {}

                    if entities.get("cities"):
                        search_params["cities"] = entities["cities"]
                    if entities.get("states"):
                        search_params["states"] = entities["states"]
                    if entities.get("localities"):
                        search_params["localities"] = entities["localities"]
                    if filters.get("must_have"):
                        search_params["required_amenities"] = filters["must_have"]
                    if filters.get("nice_to_have"):
                        search_params["preferred_amenities"] = filters["nice_to_have"]
                    if entities.get("room_types"):
                        search_params["room_types"] = entities["room_types"]
                    if entities.get("price_range"):
                        search_params["price_range"] = entities["price_range"]

                    candidate_hotels = self.db_service.search_hotels(**search_params)
                    logger.warning(f"🔧 FALLBACK DB: Found {len(candidate_hotels)} hotels with search params")

            # Remove duplicates based on property_id
            seen_ids = set()
            unique_hotels = []
            for hotel in candidate_hotels:
                hotel_id = hotel.get('property_id')
                if hotel_id and hotel_id not in seen_ids:
                    seen_ids.add(hotel_id)
                    unique_hotels.append(hotel)

            logger.info(f"After deduplication: {len(unique_hotels)} unique hotels")

            # AI GEOGRAPHIC INTELLIGENCE VALIDATION
            entities = analysis.get("entities", {})
            cities = entities.get("cities", [])
            amenities = entities.get("amenities", [])

            # Validate query logic using AI geographic intelligence
            if self.geographic_intelligence_service:
                validation = self.geographic_intelligence_service.validate_query_logic(query, cities, amenities)

                if not validation['is_valid']:
                    logger.error(f"🧠 GEOGRAPHIC INTELLIGENCE: Query failed logic validation")
                    for error in validation['errors']:
                        logger.error(f"  ❌ {error}")

                    # Return intelligent error response with suggestions
                    return {
                        'hotels': [],
                        'total_results': 0,
                        'ai_powered': True,
                        'geographic_intelligence_applied': True,
                        'logic_errors': validation['errors'],
                        'suggestions': validation['suggestions'],
                        'message': 'Query contains geographic impossibilities. See suggestions for alternatives.'
                    }

            if cities:
                logger.info(f"STRICT FILTERING: Only hotels from cities: {cities}")
                geo_filtered_hotels = self._apply_strict_geographic_filtering(unique_hotels, cities)
                logger.info(f"STRICT RESULT: {len(geo_filtered_hotels)} hotels ONLY from {cities}")
                unique_hotels = geo_filtered_hotels

                # If no hotels found in specified cities, return empty rather than wrong cities
                if not unique_hotels:
                    logger.warning(f"NO HOTELS found in specified cities {cities} - returning empty results (no geographic errors)")
                    return []

            # CRITICAL: Check for BRAND STANDARD amenities first
            if amenities:
                brand_standard_hotels = self._check_brand_standard_amenities(unique_hotels, amenities, cities)

                if brand_standard_hotels:
                    logger.warning(f"🏆 BRAND STANDARDS: Found {len(brand_standard_hotels)} hotels with guaranteed {amenities}")
                    return brand_standard_hotels

                # Check if amenities are implicit geographic amenities
                if self.amenity_verification_service:
                    implicit_amenity_hotels = self._check_implicit_geographic_amenities(unique_hotels, amenities, cities)

                    if implicit_amenity_hotels:
                        logger.info(f"🏔️ IMPLICIT GEOGRAPHIC AMENITIES: Found {len(implicit_amenity_hotels)} hotels with natural {amenities}")
                        return implicit_amenity_hotels

                    logger.info(f"Applying real-time amenity verification for: {amenities}")
                    verified_hotels = self._apply_amenity_verification(unique_hotels, amenities)
                    logger.info(f"After amenity verification: {len(verified_hotels)} hotels with required amenities")
                    return verified_hotels

            # Check if query involves brand standard amenities - if so, return all results
            brand_standard_amenities = ['breakfast', 'wifi', 'geyser', 'security']
            has_brand_standard = any(amenity in brand_standard_amenities for amenity in amenities)

            if has_brand_standard:
                logger.info(f"Query involves brand standard amenities {amenities}, returning full portfolio: {len(unique_hotels)} hotels")
                return unique_hotels  # Return all results for brand standards
            else:
                return unique_hotels[:50]  # Limit to top 50 for other queries

        except Exception as e:
            logger.error(f"Enhanced search execution failed: {str(e)}")
            return []

    def _enhance_with_api_data(self, results: List[Dict[str, Any]], analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Enhance results with detailed API data
        """
        enhanced_results = []

        for hotel in results:
            try:
                # Get detailed amenity data from API
                api_data = self.hotel_service.get_hotel_details(hotel['property_id'])

                if api_data:
                    hotel['api_amenities'] = api_data.get('amenities', {})
                    hotel['room_amenities'] = api_data.get('room_amenities', {})
                    hotel['detailed_info'] = api_data

                # Calculate relevance score
                hotel['relevance_score'] = self._calculate_relevance(hotel, analysis)

                enhanced_results.append(hotel)

            except Exception as e:
                logger.error(f"Failed to enhance hotel {hotel.get('property_id')}: {str(e)}")
                enhanced_results.append(hotel)

        # Sort by relevance score
        enhanced_results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)

        return enhanced_results

    def _calculate_relevance(self, hotel: Dict[str, Any], analysis: Dict[str, Any]) -> float:
        """
        Calculate relevance score for a hotel based on query analysis
        """
        score = 0.0

        entities = analysis.get("entities", {})
        filters = analysis.get("filters", {})

        # Location match bonus with alias support
        if entities.get("cities"):
            hotel_city = hotel.get("city_name", "").lower()
            target_cities = []

            # Expand cities to include aliases
            city_aliases = self._get_city_aliases()
            for city in entities["cities"]:
                city_lower = city.lower()
                if city_lower in city_aliases:
                    target_cities.extend([c.lower() for c in city_aliases[city_lower]])
                else:
                    target_cities.append(city_lower)

            if hotel_city in target_cities:
                score += 0.3

        # Required amenities match
        required_amenities = filters.get("must_have", [])
        if required_amenities:
            matched_amenities = 0
            total_amenities = len(required_amenities)

            for amenity in required_amenities:
                if self._hotel_has_amenity(hotel, amenity):
                    matched_amenities += 1

            if total_amenities > 0:
                score += (matched_amenities / total_amenities) * 0.4

        # Preferred amenities bonus
        preferred_amenities = filters.get("nice_to_have", [])
        if preferred_amenities:
            for amenity in preferred_amenities:
                if self._hotel_has_amenity(hotel, amenity):
                    score += 0.1

        # Hotel name match bonus
        if entities.get("hotels"):
            hotel_name = hotel.get("name", "").lower()
            for query_hotel in entities["hotels"]:
                if query_hotel.lower() in hotel_name:
                    score += 0.2

        return min(score, 1.0)  # Cap at 1.0

    def _hotel_has_amenity(self, hotel: Dict[str, Any], amenity: str) -> bool:
        """
        Check if hotel has a specific amenity, considering brand standards
        """
        amenity_lower = amenity.lower()

        # First check if this is a guaranteed brand standard amenity
        brand_standard_amenities = {
            'breakfast': ['breakfast', 'free breakfast', 'complimentary breakfast'],
            'wifi': ['wifi', 'wi-fi', 'internet', 'wireless', 'free wifi'],
            'geyser': ['geyser', 'hot water', 'water heater'],
            'security': ['security', '24 hour security', '24/7 security']
        }

        # If it's a brand standard amenity, ALL Treebo hotels have it
        for brand_amenity, keywords in brand_standard_amenities.items():
            if any(keyword in amenity_lower for keyword in keywords):
                logger.info(f"Amenity '{amenity}' is a Treebo brand standard - available at all properties")
                return True

        # Check in API amenities
        api_amenities = hotel.get('api_amenities', {})
        if isinstance(api_amenities, dict):
            amenity_text = json.dumps(api_amenities).lower()
            if amenity_lower in amenity_text:
                return True

        # Check in room amenities
        room_amenities = hotel.get('room_amenities', {})
        if isinstance(room_amenities, dict):
            room_text = json.dumps(room_amenities).lower()
            if amenity_lower in room_text:
                return True

        # Check if hotel service has brand standards service
        if (hasattr(self, 'hotel_service') and
            self.hotel_service and
            hasattr(self.hotel_service, 'brand_standards_service') and
            self.hotel_service.brand_standards_service):

            # Check against brand standards database
            if self.hotel_service.brand_standards_service.is_guaranteed_amenity(amenity_lower, 'treebo'):
                logger.info(f"Amenity '{amenity}' confirmed as guaranteed by brand standards service")
                return True

        return False

    def _generate_response(self, analysis: Dict[str, Any], results: List[Dict[str, Any]], original_query: str) -> Dict[str, Any]:
        """
        Generate intelligent response based on analysis and results
        """
        response = {
            "query": original_query,
            "analysis": analysis,
            "results": results,
            "total_results": len(results),
            "ai_powered": analysis.get("ai_powered", False)
        }

        # Generate natural language summary
        if self.client and analysis.get("ai_powered"):
            response["summary"] = self._generate_ai_summary(analysis, results, original_query)
        else:
            response["summary"] = self._generate_basic_summary(results, original_query)

        # Add suggestions
        response["suggestions"] = self._generate_suggestions(analysis, results)

        return response

    def _generate_ai_summary(self, analysis: Dict[str, Any], results: List[Dict[str, Any]], query: str) -> str:
        """
        Generate AI-powered summary of results
        """
        try:
            results_summary = []
            for hotel in results[:5]:  # Top 5 results
                results_summary.append({
                    "name": hotel.get("name"),
                    "city": hotel.get("city_name"),
                    "relevance": hotel.get("relevance_score", 0)
                })

            prompt = f"""
Based on the user query "{query}" and the search results, provide a helpful summary.

Search Results: {json.dumps(results_summary, indent=2)}
Total Results: {len(results)}

Provide a natural, conversational summary that:
1. Acknowledges the user's request
2. Summarizes the key findings
3. Highlights the most relevant options
4. Suggests next steps if appropriate

Keep it concise but informative.
"""

            response = self.client.chat.completions.create(
                model=Config.OPENAI_MODEL,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"AI summary generation failed: {str(e)}")
            return self._generate_basic_summary(results, query)

    def _generate_basic_summary(self, results: List[Dict[str, Any]], query: str) -> str:
        """
        Generate basic summary when AI is not available
        """
        if not results:
            return f"No hotels found matching your query: '{query}'. Please try different search criteria."

        if len(results) == 1:
            hotel = results[0]
            return f"Found 1 hotel matching your criteria: {hotel.get('name')} in {hotel.get('city_name')}."

        cities = list(set([r.get('city_name') for r in results if r.get('city_name')]))
        city_text = f" across {len(cities)} cities" if len(cities) > 1 else f" in {cities[0]}" if cities else ""

        return f"Found {len(results)} hotels{city_text} matching your criteria. Top recommendation: {results[0].get('name')}."

    def _generate_suggestions(self, analysis: Dict[str, Any], results: List[Dict[str, Any]]) -> List[str]:
        """
        Generate helpful suggestions for the user
        """
        suggestions = []

        if not results:
            suggestions.append("Try broadening your search criteria")
            suggestions.append("Check spelling of city or hotel names")
            suggestions.append("Consider nearby cities")
        elif len(results) > 10:
            suggestions.append("Narrow down your search with more specific criteria")
            suggestions.append("Add amenity preferences to filter results")

        # Add amenity-based suggestions
        entities = analysis.get("entities", {})
        if not entities.get("amenities"):
            suggestions.append("Specify amenities like 'swimming pool', 'spa', or 'parking'")

        return suggestions

    def _extract_hotel_names(self, query: str) -> List[str]:
        """Extract hotel names from query"""
        # This is a simplified version - in practice, you'd use the database
        hotel_patterns = [
            r'treebo\s+([a-zA-Z\s]+)',
            r'hotel\s+([a-zA-Z\s]+)',
        ]

        hotels = []
        for pattern in hotel_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            hotels.extend([match.strip() for match in matches])

        return hotels

    def _extract_cities(self, query: str) -> List[str]:
        """Enhanced city extraction using city alias service"""
        found_cities = []

        if not query:
            return found_cities

        # Use enhanced city alias service if available
        if self.city_alias_service:
            # Split query into words and check each for city matches
            words = query.lower().split()

            # Use enhanced city resolution that handles both states and cities
            for word in words:
                resolved_cities = self.city_alias_service.resolve_city_name_enhanced(word)
                if resolved_cities:
                    for city in resolved_cities:
                        if city not in found_cities:
                            found_cities.append(city)
                    logger.warning(f"🏔️ ENHANCED RESOLUTION: '{word}' → {resolved_cities}")

            # Try multi-word combinations for states/cities
            for i in range(len(words) - 1):
                two_word = f"{words[i]} {words[i+1]}"

                # Try as state first
                state_cities = self.city_alias_service.resolve_state_to_cities(two_word)
                if state_cities:
                    for city in state_cities:
                        if city not in found_cities:
                            found_cities.append(city)
                    logger.info(f"Resolved two-word state '{two_word}' to cities: {state_cities}")

                # Try as city
                resolved_city = self.city_alias_service.resolve_city_name(two_word)
                if resolved_city and resolved_city not in found_cities:
                    found_cities.append(resolved_city)
                    logger.info(f"Resolved two-word '{two_word}' to city '{resolved_city}'")

            # If no direct matches, try fuzzy matching
            if not found_cities:
                for word in words:
                    if len(word) > 3:  # Only check meaningful words
                        similar_cities = self.city_alias_service.find_similar_cities(word, threshold=75)
                        for match in similar_cities[:2]:  # Take top 2 matches
                            if match['city'] not in found_cities:
                                found_cities.append(match['city'])
                                logger.info(f"Fuzzy matched '{word}' to '{match['city']}' (score: {match['similarity']})")

        # Fallback to database service if city alias service not available
        if not found_cities and self.db_service:
            available_cities = self.db_service.get_all_cities()
            query_lower = query.lower()

            for city in available_cities:
                if city.lower() in query_lower:
                    found_cities.append(city)

        return found_cities

    def _apply_amenity_verification(self, hotels: List[Dict[str, Any]], required_amenities: List[str]) -> List[Dict[str, Any]]:
        """
        Apply real-time amenity verification to filter hotels
        """
        if not hotels or not required_amenities or not self.amenity_verification_service:
            return hotels

        verified_hotels = []

        # Extract property IDs for batch verification
        property_ids = [hotel.get('property_id') for hotel in hotels if hotel.get('property_id')]

        if not property_ids:
            logger.warning("No property IDs found in hotels for amenity verification")
            return hotels

        logger.info(f"Verifying amenities {required_amenities} for {len(property_ids)} hotels")

        # Perform batch verification
        verification_results = self.amenity_verification_service.batch_verify_hotels(
            property_ids, required_amenities
        )

        # Filter hotels based on verification results
        for hotel in hotels:
            property_id = hotel.get('property_id')
            if property_id in verification_results:
                verification = verification_results[property_id]

                # Only include hotels that have ALL required amenities
                if verification.get('all_amenities_verified', False):
                    # Add verification details to hotel data
                    hotel['amenity_verification'] = verification
                    hotel['matched_amenities'] = verification.get('matched_amenities', [])
                    hotel['verification_score'] = verification.get('verification_score', 0)
                    verified_hotels.append(hotel)

                    logger.info(f"✅ Hotel {property_id} verified with amenities: {verification.get('matched_amenities', [])}")
                else:
                    missing = verification.get('missing_amenities', [])
                    logger.info(f"❌ Hotel {property_id} missing amenities: {missing}")
            else:
                logger.warning(f"No verification result for hotel {property_id}")

        # Sort by verification score (highest first)
        verified_hotels.sort(key=lambda x: x.get('verification_score', 0), reverse=True)

        return verified_hotels

    def _check_implicit_geographic_amenities(self, hotels: List[Dict[str, Any]], amenities: List[str], cities: List[str]) -> List[Dict[str, Any]]:
        """
        Check if requested amenities are implicitly available due to geography
        CRITICAL for mountain view in mountain cities - ALL hotels have it naturally
        """
        if not self.geographic_intelligence_service or not cities:
            return []

        implicit_hotels = []

        # Check each city to see if it naturally provides the requested amenities
        for city in cities:
            city_provides_all_amenities = True

            for amenity in amenities:
                amenity_lower = amenity.lower()

                # Check for view-related amenities
                if any(view_word in amenity_lower for view_word in ['view', 'mountain', 'beach', 'sea', 'ocean', 'hill', 'valley']):
                    if not self.geographic_intelligence_service.does_city_provide_view_naturally(city, amenity):
                        city_provides_all_amenities = False
                        break
                else:
                    # Non-view amenity, can't be implicit
                    city_provides_all_amenities = False
                    break

            if city_provides_all_amenities:
                # ALL hotels in this city have the requested view amenities naturally
                city_hotels = [hotel for hotel in hotels if hotel.get('city_name', '').lower() == city.lower()]

                # Add implicit amenity information
                for hotel in city_hotels:
                    hotel['implicit_amenities'] = amenities
                    hotel['implicit_reason'] = f"Natural {'/'.join(amenities)} in {city}"
                    hotel['verification_score'] = 1.0  # Perfect score for natural amenities

                implicit_hotels.extend(city_hotels)
                logger.info(f"🏔️ IMPLICIT AMENITIES: ALL {len(city_hotels)} hotels in {city} have natural {amenities}")

        return implicit_hotels

    def _check_brand_standard_amenities(self, hotels: List[Dict[str, Any]], amenities: List[str], cities: List[str]) -> List[Dict[str, Any]]:
        """
        Check if ALL requested amenities are GUARANTEED brand standards
        If so, return ALL hotels since they're guaranteed to have these amenities
        """
        if not amenities or not self.hotel_service:
            return []

        # Check if ALL amenities are brand standards
        all_brand_standards = True
        guaranteed_amenities = []

        for amenity in amenities:
            if self._is_guaranteed_brand_standard(amenity):
                guaranteed_amenities.append(amenity)
                logger.warning(f"🏆 BRAND STANDARD: '{amenity}' is GUARANTEED at ALL Treebo hotels")
            else:
                all_brand_standards = False
                logger.info(f"❌ NOT BRAND STANDARD: '{amenity}' requires verification")
                break

        if all_brand_standards and guaranteed_amenities:
            # ALL requested amenities are brand standards - return ALL hotels
            brand_standard_hotels = []

            for hotel in hotels:
                # Add brand standard information
                hotel['brand_standard_amenities'] = guaranteed_amenities
                hotel['brand_standard_reason'] = f"Guaranteed Treebo brand standards: {', '.join(guaranteed_amenities)}"
                hotel['verification_score'] = 1.0  # Perfect score for guaranteed amenities
                hotel['guaranteed'] = True
                brand_standard_hotels.append(hotel)

            logger.warning(f"🏆 BRAND STANDARDS APPLIED: ALL {len(brand_standard_hotels)} hotels have guaranteed {guaranteed_amenities}")
            return brand_standard_hotels

        return []

    def _is_guaranteed_brand_standard(self, amenity: str) -> bool:
        """Check if an amenity is a guaranteed Treebo brand standard"""
        amenity_lower = amenity.lower().strip()

        # Comprehensive brand standard mappings
        brand_standard_keywords = {
            'breakfast': ['breakfast', 'free breakfast', 'complimentary breakfast', 'morning meal'],
            'wifi': ['wifi', 'wi-fi', 'internet', 'wireless', 'free wifi', 'complimentary wifi'],
            'geyser': ['geyser', 'hot water', 'water heater', 'hot water supply'],
            'security': ['security', '24 hour security', '24/7 security', 'round the clock security']
        }

        # Check against brand standard keywords
        for brand_amenity, keywords in brand_standard_keywords.items():
            if any(keyword in amenity_lower for keyword in keywords):
                logger.warning(f"🏆 CONFIRMED: '{amenity}' matches brand standard '{brand_amenity}'")
                return True

        # Check with brand standards service if available
        if (self.hotel_service and
            hasattr(self.hotel_service, 'brand_standards_service') and
            self.hotel_service.brand_standards_service):

            if self.hotel_service.brand_standards_service.is_guaranteed_amenity(amenity_lower, 'treebo'):
                logger.warning(f"🏆 DATABASE CONFIRMED: '{amenity}' is guaranteed by brand standards service")
                return True

        return False

    def _apply_strict_geographic_filtering(self, hotels: List[Dict[str, Any]], target_cities: List[str]) -> List[Dict[str, Any]]:
        """
        ULTRA-STRICT geographic filtering - ZERO tolerance for geographic errors
        NO MIXING of cities, NO stale data, NO geographic mistakes
        """
        if not hotels or not target_cities:
            return hotels

        # Normalize target cities for EXACT matching only
        normalized_target_cities = set()
        for city in target_cities:
            city_clean = city.lower().strip()
            normalized_target_cities.add(city_clean)

            # Add ONLY exact aliases (no fuzzy matching to prevent errors)
            if self.city_alias_service:
                resolved_city = self.city_alias_service.resolve_city_name(city)
                if resolved_city:
                    normalized_target_cities.add(resolved_city.lower().strip())

        logger.warning(f"🔒 STRICT GEOGRAPHIC FILTER ACTIVATED")
        logger.warning(f"🎯 TARGET CITIES: {target_cities}")
        logger.warning(f"🔍 NORMALIZED TARGETS: {normalized_target_cities}")

        filtered_hotels = []
        rejected_hotels = []

        for hotel in hotels:
            hotel_city = hotel.get('city_name', '').lower().strip()
            hotel_name = hotel.get('name', 'Unknown')

            # ULTRA-STRICT: Only EXACT city matches allowed
            if hotel_city in normalized_target_cities:
                filtered_hotels.append(hotel)
                logger.info(f"✅ ACCEPTED: {hotel_name} in {hotel.get('city_name')} (EXACT MATCH)")
            else:
                rejected_hotels.append({
                    'name': hotel_name,
                    'city': hotel.get('city_name', 'Unknown'),
                    'reason': f"GEOGRAPHIC ERROR PREVENTED: '{hotel.get('city_name')}' ≠ {target_cities}"
                })
                logger.error(f"❌ REJECTED: {hotel_name} in {hotel.get('city_name')} (WRONG CITY - PREVENTED GEOGRAPHIC ERROR)")

        # Log ALL rejections to ensure transparency
        if rejected_hotels:
            logger.error(f"🚫 GEOGRAPHIC FILTER: Rejected {len(rejected_hotels)} hotels from WRONG cities:")
            for rejected in rejected_hotels:
                logger.error(f"  ❌ {rejected['name']} in {rejected['city']} - {rejected['reason']}")

        logger.warning(f"🎯 STRICT RESULT: {len(filtered_hotels)} hotels from CORRECT cities ONLY")
        logger.warning(f"🛡️ GEOGRAPHIC ACCURACY: 100% - NO MIXING ALLOWED")

        # If no hotels found, log this clearly
        if not filtered_hotels:
            logger.error(f"⚠️ NO HOTELS found in specified cities {target_cities}")
            logger.error(f"🔍 This is CORRECT behavior - better no results than wrong cities!")

        return filtered_hotels

    def _get_city_aliases(self) -> Dict[str, List[str]]:
        """Get comprehensive city alias mapping"""
        return {
            # Major Metro Cities
            'mumbai': ['Mumbai'],
            'bombay': ['Mumbai'],
            'bom': ['Mumbai'],

            'bangalore': ['Bangalore'],
            'bengaluru': ['Bangalore'],
            'blr': ['Bangalore'],

            'delhi': ['Delhi', 'New Delhi'],
            'new delhi': ['New Delhi', 'Delhi'],
            'gurgaon': ['Gurgaon'],
            'gurugram': ['Gurgaon'],
            'noida': ['Noida'],
            'faridabad': ['Faridabad'],

            'chennai': ['Chennai'],
            'madras': ['Chennai'],

            'kolkata': ['Kolkata'],
            'calcutta': ['Kolkata'],

            'hyderabad': ['Hyderabad'],
            'secunderabad': ['Hyderabad'],
            'cyberabad': ['Hyderabad'],

            'pune': ['Pune'],
            'poona': ['Pune'],

            # Other major cities with aliases
            'kochi': ['Kochi'],
            'cochin': ['Kochi'],
            'thiruvananthapuram': ['Thiruvananthapuram'],
            'trivandrum': ['Thiruvananthapuram'],
            'mysore': ['Mysore'],
            'mysuru': ['Mysore'],
            'mangalore': ['Mangalore'],
            'mangaluru': ['Mangalore'],
            'vadodara': ['Vadodara'],
            'baroda': ['Vadodara'],
            'visakhapatnam': ['Visakhapatnam'],
            'vizag': ['Visakhapatnam'],
            'coimbatore': ['Coimbatore'],
            'cbe': ['Coimbatore'],
            'pondicherry': ['Pondicherry'],
            'puducherry': ['Pondicherry'],
            'goa': ['Goa'],
            'panaji': ['Goa'],
            'margao': ['Goa']
        }

    def _extract_amenities(self, query: str) -> List[str]:
        """Extract amenities from query with comprehensive keyword matching"""
        amenity_keywords = {
            # Basic amenities (very common)
            'breakfast': ['breakfast', 'free breakfast', 'complimentary breakfast', 'morning meal'],
            'wifi': ['wifi', 'wi-fi', 'internet', 'wireless', 'free wifi'],
            'ac': ['ac', 'air conditioning', 'aircon', 'air conditioned'],
            'tv': ['tv', 'television', 'flat screen', 'cable tv'],
            'geyser': ['geyser', 'hot water', 'water heater'],
            'fan': ['fan', 'ceiling fan'],

            # Hotel services
            'elevator': ['elevator', 'lift'],
            'room_service': ['room service', 'in-room dining', 'room services'],
            'laundry': ['laundry', 'guest laundry', 'dry cleaning', 'washing'],
            'security': ['security', '24 hour security', '24/7 security', 'round the clock security'],
            'card_payment': ['card payment', 'credit card', 'debit card', 'cashless'],
            'ironing': ['ironing', 'ironing board', 'iron'],

            # Room amenities
            'toiletries': ['toiletries', 'complimentary toiletries', 'bathroom amenities'],
            'furniture': ['furniture', 'study table', 'chair', 'sofa', 'coffee table'],
            'cupboards': ['cupboards', 'wardrobe', 'storage', 'closet'],
            'windows': ['windows', 'natural light'],
            'intercom': ['intercom', 'phone'],

            # Facilities
            'parking': ['parking', 'valet parking', 'car park', 'vehicle parking'],
            'restaurant': ['restaurant', 'dining', 'food', 'in-house restaurant', 'multi cuisine'],
            'pantry': ['pantry', 'kitchen', 'kitchenette'],
            'travel_desk': ['travel desk', 'travel assistance', 'tour desk'],
            'wheelchair': ['wheelchair', 'wheelchair accessible', 'accessibility'],

            # Safety
            'smoke_alarm': ['smoke alarm', 'fire alarm', 'fire safety'],
            'mosquito_repellent': ['mosquito repellent', 'pest control'],

            # Premium amenities
            'swimming_pool': ['swimming pool', 'pool', 'swimming', 'swim'],
            'spa': ['spa', 'massage', 'wellness', 'ayurveda', 'spa services'],
            'banquet': ['banquet', 'banquet hall', 'conference', 'meeting room', 'event hall', 'function hall'],
            'gym': ['gym', 'fitness', 'fitness center', 'exercise', 'workout'],
            'bar': ['bar', 'lounge', 'pub'],

            # Business amenities
            'conference_room': ['conference room', 'meeting room', 'boardroom', 'conference hall'],
            'business_center': ['business center', 'business services'],

            # General terms
            'luxury': ['luxury', 'premium', 'deluxe', 'suite'],
            'budget': ['budget', 'economy', 'affordable', 'cheap'],
            'business': ['business', 'corporate', 'executive']
        }

        found_amenities = []
        query_lower = query.lower()

        for amenity, keywords in amenity_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    found_amenities.append(amenity)
                    break

        return found_amenities

    def _extract_room_types(self, query: str) -> List[str]:
        """Extract room types from query"""
        room_keywords = {
            'twin_bed': ['twin bed', 'twin beds'],
            'double_bed': ['double bed', 'king bed', 'queen bed'],
            'single_bed': ['single bed'],
            'deluxe_room': ['deluxe room', 'deluxe'],
            'suite': ['suite', 'suites']
        }

        found_rooms = []
        query_lower = query.lower()

        for room_type, keywords in room_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    found_rooms.append(room_type)
                    break

        return found_rooms

    def _error_response(self, query: str, error: str) -> Dict[str, Any]:
        """Generate error response"""
        return {
            "query": query,
            "error": error,
            "results": [],
            "total_results": 0,
            "summary": f"Sorry, I encountered an error processing your query: {error}",
            "suggestions": [
                "Please try rephrasing your question",
                "Check your internet connection",
                "Contact support if the problem persists"
            ]
        }
