-- Migration: Create amenity cache tables
-- Description: Cache amenity data from Treebo API for better performance
-- Author: Hotel Portfolio Query Tool
-- Date: 2024-06-06

-- Create property_amenities table
CREATE TABLE IF NOT EXISTS property_amenities (
    id SERIAL PRIMARY KEY,
    property_id INTEGER NOT NULL,
    amenity_key VARCHAR(100) NOT NULL,
    amenity_name VARCHAR(100),
    amenity_value BOOLEAN DEFAULT TRUE,
    source VARCHAR(50) DEFAULT 'api',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(property_id, amenity_key)
);

-- Create room_amenities table
CREATE TABLE IF NOT EXISTS room_amenities (
    id SERIAL PRIMARY KEY,
    property_id INTEGER NOT NULL,
    room_type VARCHAR(50) NOT NULL,
    amenity_key VARCHAR(100) NOT NULL,
    amenity_name VA<PERSON><PERSON><PERSON>(100),
    amenity_value BOOLEAN DEFAULT TRUE,
    source VARCHAR(50) DEFAULT 'api',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(property_id, room_type, amenity_key)
);

-- Create amenity_cache_status table to track cache freshness
CREATE TABLE IF NOT EXISTS amenity_cache_status (
    id SERIAL PRIMARY KEY,
    property_id INTEGER UNIQUE NOT NULL,
    last_fetched TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fetch_status VARCHAR(50) DEFAULT 'success',
    error_message TEXT,
    api_response_time INTEGER, -- in milliseconds
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_property_amenities_property_id ON property_amenities(property_id);
CREATE INDEX IF NOT EXISTS idx_property_amenities_amenity_key ON property_amenities(amenity_key);
CREATE INDEX IF NOT EXISTS idx_property_amenities_source ON property_amenities(source);

CREATE INDEX IF NOT EXISTS idx_room_amenities_property_id ON room_amenities(property_id);
CREATE INDEX IF NOT EXISTS idx_room_amenities_room_type ON room_amenities(room_type);
CREATE INDEX IF NOT EXISTS idx_room_amenities_amenity_key ON room_amenities(amenity_key);
CREATE INDEX IF NOT EXISTS idx_room_amenities_source ON room_amenities(source);

CREATE INDEX IF NOT EXISTS idx_amenity_cache_status_property_id ON amenity_cache_status(property_id);
CREATE INDEX IF NOT EXISTS idx_amenity_cache_status_last_fetched ON amenity_cache_status(last_fetched);
CREATE INDEX IF NOT EXISTS idx_amenity_cache_status_fetch_status ON amenity_cache_status(fetch_status);

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_property_amenities_updated_at 
    BEFORE UPDATE ON property_amenities 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_amenities_updated_at 
    BEFORE UPDATE ON room_amenities 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_amenity_cache_status_updated_at 
    BEFORE UPDATE ON amenity_cache_status 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments to tables and columns
COMMENT ON TABLE property_amenities IS 'Cache property-level amenities from Treebo API';
COMMENT ON COLUMN property_amenities.property_id IS 'Reference to hotel property ID';
COMMENT ON COLUMN property_amenities.amenity_key IS 'Standardized amenity identifier';
COMMENT ON COLUMN property_amenities.source IS 'Source of amenity data (api, brand_standard, manual)';

COMMENT ON TABLE room_amenities IS 'Cache room-level amenities from Treebo API';
COMMENT ON COLUMN room_amenities.property_id IS 'Reference to hotel property ID';
COMMENT ON COLUMN room_amenities.room_type IS 'Room type (oak, mahogany, acacia, maple)';
COMMENT ON COLUMN room_amenities.amenity_key IS 'Standardized amenity identifier';
COMMENT ON COLUMN room_amenities.source IS 'Source of amenity data (api, brand_standard, manual)';

COMMENT ON TABLE amenity_cache_status IS 'Track amenity cache freshness and API fetch status';
COMMENT ON COLUMN amenity_cache_status.property_id IS 'Reference to hotel property ID';
COMMENT ON COLUMN amenity_cache_status.last_fetched IS 'When amenities were last fetched from API';
COMMENT ON COLUMN amenity_cache_status.fetch_status IS 'Status of last API fetch (success, error, timeout)';
COMMENT ON COLUMN amenity_cache_status.api_response_time IS 'API response time in milliseconds';
