-- Migration: Create brand_standards table
-- Description: Store Treebo and Itsy brand standard services
-- Author: Hotel Portfolio Query Tool
-- Date: 2024-06-06

-- Create brand_standards table
CREATE TABLE IF NOT EXISTS brand_standards (
    id SERIAL PRIMARY KEY,
    brand_name VARCHAR(50) NOT NULL,
    amenity_key VARCHAR(100) NOT NULL,
    amenity_name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50),
    guaranteed BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(brand_name, amenity_key)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_brand_standards_brand ON brand_standards(brand_name);
CREATE INDEX IF NOT EXISTS idx_brand_standards_amenity ON brand_standards(amenity_key);
CREATE INDEX IF NOT EXISTS idx_brand_standards_guaranteed ON brand_standards(guaranteed);
CREATE INDEX IF NOT EXISTS idx_brand_standards_category ON brand_standards(category);

-- <PERSON><PERSON> function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_brand_standards_updated_at 
    BEFORE UPDATE ON brand_standards 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert Treebo standard services
INSERT INTO brand_standards (brand_name, amenity_key, amenity_name, description, category, guaranteed) VALUES
('treebo', 'free_breakfast', 'Free Breakfast', 'Complimentary breakfast for all guests', 'dining', TRUE),
('treebo', 'geyser', 'Hot Water/Geyser', '24/7 hot water supply with geyser', 'basic_comfort', TRUE),
('treebo', 'wifi', 'WiFi', 'Free high-speed WiFi internet', 'connectivity', TRUE),
('treebo', 'free_wifi', 'Free WiFi', 'Complimentary WiFi internet access', 'connectivity', TRUE)
ON CONFLICT (brand_name, amenity_key) DO NOTHING;

-- Insert Itsy standard services
INSERT INTO brand_standards (brand_name, amenity_key, amenity_name, description, category, guaranteed) VALUES
('itsy', 'free_breakfast', 'Free Breakfast', 'Complimentary breakfast', 'dining', TRUE),
('itsy', 'geyser', 'Hot Water/Geyser', 'Hot water supply with geyser', 'basic_comfort', TRUE),
('itsy', 'wifi', 'WiFi', 'Free WiFi internet', 'connectivity', TRUE),
('itsy', 'free_wifi', 'Free WiFi', 'Complimentary WiFi internet access', 'connectivity', TRUE)
ON CONFLICT (brand_name, amenity_key) DO NOTHING;

-- Add comments to table and columns
COMMENT ON TABLE brand_standards IS 'Store brand standard services for hotel chains like Treebo and Itsy';
COMMENT ON COLUMN brand_standards.brand_name IS 'Hotel brand name (treebo, itsy, etc.)';
COMMENT ON COLUMN brand_standards.amenity_key IS 'Unique amenity identifier used in code';
COMMENT ON COLUMN brand_standards.amenity_name IS 'Human-readable amenity name';
COMMENT ON COLUMN brand_standards.description IS 'Detailed description of the amenity';
COMMENT ON COLUMN brand_standards.category IS 'Amenity category (dining, connectivity, basic_comfort, etc.)';
COMMENT ON COLUMN brand_standards.guaranteed IS 'Whether this amenity is guaranteed across all properties';
