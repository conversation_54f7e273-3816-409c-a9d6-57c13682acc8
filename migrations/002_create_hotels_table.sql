-- Migration: Create hotels table
-- Description: Store hotel portfolio data from CSV
-- Author: Hotel Portfolio Query Tool
-- Date: 2024-06-06

-- Create hotels table
CREATE TABLE IF NOT EXISTS hotels (
    id SERIAL PRIMARY KEY,
    property_id INTEGER UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    city_name VARCHAR(100) NOT NULL,
    state VARCHAR(100),
    country VARCHAR(100) DEFAULT 'India',
    brand VARCHAR(50),
    property_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'active',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_hotels_property_id ON hotels(property_id);
CREATE INDEX IF NOT EXISTS idx_hotels_city_name ON hotels(city_name);
CREATE INDEX IF NOT EXISTS idx_hotels_state ON hotels(state);
CREATE INDEX IF NOT EXISTS idx_hotels_brand ON hotels(brand);
CREATE INDEX IF NOT EXISTS idx_hotels_name ON hotels(name);
CREATE INDEX IF NOT EXISTS idx_hotels_status ON hotels(status);

-- Create GIN index for full-text search on hotel names
CREATE INDEX IF NOT EXISTS idx_hotels_name_gin ON hotels USING gin(to_tsvector('english', name));

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_hotels_updated_at 
    BEFORE UPDATE ON hotels 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments to table and columns
COMMENT ON TABLE hotels IS 'Store hotel portfolio data including property details and location information';
COMMENT ON COLUMN hotels.property_id IS 'Unique property identifier from Treebo system';
COMMENT ON COLUMN hotels.name IS 'Hotel name';
COMMENT ON COLUMN hotels.city_name IS 'City where hotel is located';
COMMENT ON COLUMN hotels.state IS 'State where hotel is located';
COMMENT ON COLUMN hotels.brand IS 'Hotel brand (Treebo, Itsy, etc.)';
COMMENT ON COLUMN hotels.property_type IS 'Type of property (hotel, resort, etc.)';
COMMENT ON COLUMN hotels.status IS 'Current status of the property (active, inactive, etc.)';
COMMENT ON COLUMN hotels.latitude IS 'Latitude coordinate for mapping';
COMMENT ON COLUMN hotels.longitude IS 'Longitude coordinate for mapping';
