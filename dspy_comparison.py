import dspy

# Configure DSPy with OpenAI
lm = dspy.LM('openai/gpt-4o-mini',
             api_key='********************************************************************************************************************************************************************')
dspy.configure(lm=lm)

def compare_reasoning_tasks():
    """Compare Predict vs ChainOfThought on tasks requiring reasoning"""
    
    print("=== MATHEMATICAL REASONING ===")
    math_predict = dspy.Predict("problem -> answer")
    math_cot = dspy.ChainOfThought("problem -> answer")
    
    problem = "If a country has 5 major cities, and each city has 3 districts, and each district has 2 administrative centers, how many administrative centers are there in total?"
    
    print(f"Problem: {problem}")
    print(f"Predict: {math_predict(problem=problem).answer}")
    
    cot_result = math_cot(problem=problem)
    print(f"ChainOfThought: {cot_result.answer}")
    if hasattr(cot_result, 'rationale'):
        print(f"Reasoning: {cot_result.rationale}")
    
    print("\n" + "="*60 + "\n")
    
    print("=== LOGICAL DEDUCTION ===")
    logic_predict = dspy.Predict("clues -> conclusion")
    logic_cot = dspy.ChainOfThought("clues -> conclusion")
    
    clues = "City A is north of City B. City B is the capital. City C is south of the capital. City A has the largest population. Which city is in the middle geographically?"
    
    print(f"Clues: {clues}")
    print(f"Predict: {logic_predict(clues=clues).conclusion}")
    
    cot_result = logic_cot(clues=clues)
    print(f"ChainOfThought: {cot_result.conclusion}")
    if hasattr(cot_result, 'rationale'):
        print(f"Reasoning: {cot_result.rationale}")
    
    print("\n" + "="*60 + "\n")
    
    print("=== MULTI-STEP GEOGRAPHY ===")
    geo_predict = dspy.Predict("geographic_puzzle -> answer")
    geo_cot = dspy.ChainOfThought("geographic_puzzle -> answer")
    
    puzzle = "Start at the capital of the country known for the Great Wall. Travel west to the capital of the country famous for Bollywood. Then go northwest to the capital of the country that was the center of the Ottoman Empire. What's your final destination?"
    
    print(f"Puzzle: {puzzle}")
    print(f"Predict: {geo_predict(geographic_puzzle=puzzle).answer}")
    
    cot_result = geo_cot(geographic_puzzle=puzzle)
    print(f"ChainOfThought: {cot_result.answer}")
    if hasattr(cot_result, 'rationale'):
        print(f"Reasoning: {cot_result.rationale}")

if __name__ == "__main__":
    compare_reasoning_tasks()
