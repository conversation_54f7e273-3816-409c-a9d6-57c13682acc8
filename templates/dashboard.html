<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Hotel Search Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <style>
        body {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFB74D 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 1rem;
            min-height: 95vh;
        }

        .header {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 20px 20px 0 0;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .search-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .results-panel {
            padding: 1rem;
            max-height: 70vh;
            overflow-y: auto;
        }

        .hotel-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }

        .hotel-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .hotel-name {
            color: #667eea;
            font-weight: 600;
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }

        .amenity-section {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .amenity-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            margin: 0.2rem;
            display: inline-block;
        }

        .property-amenity {
            background: #28a745;
        }

        .room-amenity {
            background: #17a2b8;
        }

        .room-type-card {
            background: #e9ecef;
            border-radius: 8px;
            padding: 0.8rem;
            margin: 0.5rem 0;
            border-left: 3px solid #6c757d;
        }

        .acacia { border-left-color: #8B4513; }
        .oak { border-left-color: #D2691E; }
        .maple { border-left-color: #CD853F; }
        .mahogany { border-left-color: #A0522D; }

        .relevance-score {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .confidence-high {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .confidence-medium {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }

        .high-confidence-badge {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 0.5rem;
        }

        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .btn-search {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            border: none;
            border-radius: 15px;
            padding: 0.8rem 2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
            color: white;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .ai-indicator {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            display: inline-block;
        }

        .select2-container--default .select2-selection--multiple {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            min-height: 45px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #667eea;
            border: none;
            border-radius: 15px;
            color: white;
            padding: 3px 8px;
        }

        .search-input-container {
            position: relative;
        }

        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        }

        .suggestion-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .suggestion-item:hover {
            background-color: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-text {
            font-weight: 500;
            color: #333;
        }

        .suggestion-description {
            font-size: 0.85em;
            color: #666;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="header">
            <div class="row align-items-center">
                <div class="col-md-2">
                    <div class="treebo-logo">
                        <span style="font-size: 1.8rem; font-weight: 700; color: white; font-family: 'Arial', sans-serif;">
                            Treebo
                        </span>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2><i class="fas fa-search"></i> Advanced Hotel Search Dashboard</h2>
                    <p class="mb-0">Intelligent search with property and room-level amenity analysis</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="ai-indicator" id="ai-model-indicator">
                        <i class="fas fa-robot"></i> Loading AI Model...
                    </div>
                    <div class="mt-2">
                        <small class="text-light" id="ai-description">
                            <i class="fas fa-shield-alt"></i> Loading AI capabilities...
                        </small>
                    </div>
                    <div class="mt-1">
                        <small class="text-light" id="db-status">
                            <i class="fas fa-database"></i> Checking database...
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-0">
            <!-- Search Panel -->
            <div class="col-md-4">
                <div class="search-panel">
                    <!-- AI Status Panel -->
                    <div class="filter-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                        <h6><i class="fas fa-robot"></i> AI Intelligence Engine</h6>
                        <div id="ai-model-name" style="font-weight: bold; margin: 5px 0;">Loading...</div>
                        <div id="ai-capabilities" style="font-size: 0.85rem; opacity: 0.9;">
                            <!-- AI capabilities will be loaded here -->
                        </div>
                        <div class="mt-2" style="font-size: 0.8rem;">
                            <span id="brand-standards-status">
                                <i class="fas fa-database"></i> Loading brand standards...
                            </span>
                        </div>
                    </div>

                    <h5><i class="fas fa-filter"></i> Search Filters</h5>

                    <!-- Natural Language Query -->
                    <div class="filter-section">
                        <label class="form-label"><i class="fas fa-comments"></i> Natural Language Query</label>
                        <div class="search-input-container">
                            <textarea id="naturalQuery" class="form-control" rows="3"
                                    placeholder="e.g., Find hotels with swimming pool and spa in Mumbai with acacia rooms"
                                    onkeypress="handleSearchKeyPress(event)"
                                    oninput="handleSearchInput(event)"></textarea>
                            <div id="searchSuggestions" class="search-suggestions" style="display: none;">
                                <!-- Dynamic suggestions will be populated here -->
                            </div>
                        </div>
                        <button class="btn btn-search w-100 mt-2" onclick="processNaturalQuery()">
                            <i class="fas fa-brain"></i> AI Search (Press Enter)
                        </button>

                        <!-- Real-time Data Info -->
                        <div class="alert alert-info mt-2 mb-0">
                            <i class="fas fa-sync-alt"></i> <strong>Real-time Data</strong><br>
                            <small>All results are fetched fresh from live APIs - no stale data!</small>
                        </div>
                    </div>

                    <!-- Advanced Filters -->
                    <div class="filter-section">
                        <label class="form-label"><i class="fas fa-map-marker-alt"></i> Cities</label>
                        <select id="cityFilter" class="form-select" multiple>
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>

                    <div class="filter-section">
                        <label class="form-label"><i class="fas fa-bed"></i> Room Types</label>
                        <select id="roomTypeFilter" class="form-select" multiple>
                            <option value="acacia">Acacia</option>
                            <option value="oak">Oak</option>
                            <option value="maple">Maple</option>
                            <option value="mahogany">Mahogany</option>
                        </select>
                    </div>

                    <div class="filter-section">
                        <label class="form-label"><i class="fas fa-star"></i> Property Amenities</label>
                        <select id="propertyAmenityFilter" class="form-select" multiple>
                            <option value="swimming_pool">Swimming Pool</option>
                            <option value="spa">Spa</option>
                            <option value="banquet">Banquet Hall</option>
                            <option value="parking">Parking</option>
                            <option value="restaurant">Restaurant</option>
                            <option value="gym">Gym</option>
                            <option value="wifi">WiFi</option>
                            <option value="elevator">Elevator</option>
                        </select>
                    </div>

                    <div class="filter-section">
                        <label class="form-label"><i class="fas fa-door-open"></i> Room-Level Amenities</label>
                        <select id="roomAmenityFilter" class="form-select" multiple>
                            <option value="ac">Air Conditioning</option>
                            <option value="tv">Television</option>
                            <option value="balcony">Balcony</option>
                            <option value="room_service">Room Service</option>
                            <option value="mini_bar">Mini Bar</option>
                            <option value="safe">Safe</option>
                            <option value="wifi">Room WiFi</option>
                            <option value="geyser">Hot Water/Geyser</option>
                            <option value="wardrobe">Wardrobe</option>
                            <option value="desk">Work Desk</option>
                            <option value="chair">Seating Area</option>
                            <option value="bathroom">Private Bathroom</option>
                            <option value="towels">Towels</option>
                            <option value="toiletries">Toiletries</option>
                        </select>
                        <small class="text-muted">Search amenities available in specific room types</small>
                    </div>

                    <div class="filter-section">
                        <label class="form-label"><i class="fas fa-hotel"></i> Hotel Name</label>
                        <input type="text" id="hotelNameFilter" class="form-control"
                               placeholder="Enter hotel name...">
                    </div>

                    <button class="btn btn-search w-100 mt-3" onclick="performAdvancedSearch()">
                        <i class="fas fa-search"></i> Advanced Search
                    </button>

                    <button class="btn btn-outline-secondary w-100 mt-2" onclick="clearFilters()">
                        <i class="fas fa-times"></i> Clear Filters
                    </button>

                    <!-- Enhanced Search Examples -->
                    <div class="mt-4">
                        <h6><i class="fas fa-lightbulb"></i> Try Complex Search Examples:</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="setSearchQuery('luxury hotels with swimming pool and spa in Mumbai')">
                                <i class="fas fa-swimming-pool"></i> Luxury + Pool + Spa in Mumbai
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setSearchQuery('hotels with free breakfast in Bangalore')">
                                <i class="fas fa-coffee"></i> Free Breakfast in Bangalore (Brand Standard)
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setSearchQuery('hotels in Himachal with mountain view')">
                                <i class="fas fa-mountain"></i> Mountain View in Himachal
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setSearchQuery('hotels with gym and restaurant in Delhi')">
                                <i class="fas fa-dumbbell"></i> Gym + Restaurant in Delhi
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="setSearchQuery('acacia rooms with balcony in Mumbai')">
                                <i class="fas fa-bed"></i> Room-Level: Acacia + Balcony
                            </button>
                        </div>
                    </div>

                    <!-- Enhanced Features Info -->
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-rocket"></i> <strong>Enhanced Features</strong><br>
                        <small>✅ Smart city aliases (Bombay→Mumbai)<br>
                        ✅ Real-time amenity data<br>
                        ✅ AI-powered search intelligence</small>
                    </div>

                    <!-- High Confidence Info -->
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-shield-alt"></i> <strong>Quality Assurance</strong><br>
                        <small>Only showing high-confidence results (60%+ match) for better accuracy</small>
                    </div>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="col-md-8">
                <div class="results-panel">
                    <!-- Stats Row -->
                    <div class="row mb-3" id="statsRow" style="display: none;">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="totalResults">0</div>
                                <small class="text-muted">Total Results</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="avgRelevance">0%</div>
                                <small class="text-muted">Avg Relevance</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="citiesCount">0</div>
                                <small class="text-muted">Cities</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number" id="roomTypesCount">0</div>
                                <small class="text-muted">Room Types</small>
                            </div>
                        </div>
                    </div>

                    <!-- Results Container -->
                    <div id="resultsContainer">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Use the search panel to find hotels</h5>
                            <p class="text-muted">Try natural language queries or use advanced filters</p>

                            <!-- Test Links for Detailed View -->
                            <div class="mt-4">
                                <h6 class="text-muted">Quick Test Links:</h6>
                                <a href="/hotel-details?id=1001" class="btn btn-outline-primary btn-sm me-2">
                                    <i class="fas fa-eye"></i> View Treebo Paradise Details
                                </a>
                                <a href="/hotel-details?id=1002" class="btn btn-outline-primary btn-sm me-2">
                                    <i class="fas fa-eye"></i> View Treebo Grand Palace Details
                                </a>
                                <a href="/hotel-details?id=1003" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View Treebo Royal Inn Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        // Initialize Select2 for multi-select dropdowns
        $(document).ready(function() {
            $('#cityFilter, #roomTypeFilter, #propertyAmenityFilter, #roomAmenityFilter').select2({
                placeholder: 'Select options...',
                allowClear: true
            });

            // Add immediate search on dropdown change
            $('#cityFilter, #roomTypeFilter, #propertyAmenityFilter, #roomAmenityFilter').on('change', function() {
                handleDropdownChange();
            });

            // Load cities and AI status
            loadCities();
            loadAIStatus();
        });

        async function loadCities() {
            try {
                const response = await fetch('/api/cities');
                const data = await response.json();

                if (data.status === 'success') {
                    const citySelect = $('#cityFilter');
                    citySelect.empty();

                    data.cities.forEach(city => {
                        citySelect.append(new Option(city, city));
                    });
                }
            } catch (error) {
                console.error('Error loading cities:', error);
            }
        }

        async function loadAIStatus() {
            try {
                const response = await fetch('/health');
                const data = await response.json();

                // Update header AI indicator
                document.getElementById('ai-model-indicator').innerHTML =
                    `<i class="fas fa-robot"></i> ${data.ai_intelligence.model}`;

                document.getElementById('ai-description').innerHTML =
                    `<i class="fas fa-shield-alt"></i> ${data.ai_intelligence.description.substring(0, 50)}...`;

                // Update database status
                const dbStatusText = data.database.status === 'connected' ?
                    `<i class="fas fa-database"></i> ${data.database.type} Connected` :
                    `<i class="fas fa-exclamation-triangle"></i> Database Error`;
                document.getElementById('db-status').innerHTML = dbStatusText;

                // Update AI panel
                document.getElementById('ai-model-name').textContent = data.ai_intelligence.model;

                // Update AI capabilities
                const capabilitiesHtml = data.ai_intelligence.capabilities.slice(0, 2).map(cap =>
                    `• ${cap.substring(0, 40)}...`
                ).join('<br>');
                document.getElementById('ai-capabilities').innerHTML = capabilitiesHtml;

                // Update brand standards status
                const brandStandardsText = data.ai_intelligence.brand_standards_loaded ?
                    `<i class="fas fa-check-circle"></i> ${data.ai_intelligence.total_brand_standards} brand standards loaded` :
                    `<i class="fas fa-exclamation-triangle"></i> Brand standards not loaded`;
                document.getElementById('brand-standards-status').innerHTML = brandStandardsText;

            } catch (error) {
                console.error('Error loading AI status:', error);
                document.getElementById('ai-model-indicator').innerHTML =
                    '<i class="fas fa-exclamation-triangle"></i> AI Status Error';
                document.getElementById('ai-model-name').textContent = 'Error loading AI model';
            }
        }

        async function processNaturalQuery() {
            const query = document.getElementById('naturalQuery').value.trim();

            if (!query) {
                alert('Please enter a query');
                return;
            }

            showLoading();

            try {
                const response = await fetch('/api/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    displayResults(data.result);
                } else {
                    showError(data.error || 'An error occurred');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        function handleDropdownChange() {
            // Get current filter values
            const cities = $('#cityFilter').val() || [];
            const roomTypes = $('#roomTypeFilter').val() || [];
            const propertyAmenities = $('#propertyAmenityFilter').val() || [];
            const roomAmenities = $('#roomAmenityFilter').val() || [];

            // Only trigger search if at least one filter is selected
            if (cities.length > 0 || roomTypes.length > 0 || propertyAmenities.length > 0 || roomAmenities.length > 0) {
                console.log('🔄 Dropdown changed - triggering immediate search');
                performAdvancedSearch();
            }
        }

        async function performAdvancedSearch() {
            const filters = {
                cities: $('#cityFilter').val() || [],
                room_types: $('#roomTypeFilter').val() || [],
                property_amenities: $('#propertyAmenityFilter').val() || [],
                room_amenities: $('#roomAmenityFilter').val() || [],
                hotel_name: document.getElementById('hotelNameFilter').value.trim()
            };

            showLoading();

            try {
                const response = await fetch('/api/smart-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(filters)
                });

                const data = await response.json();

                if (data.status === 'success') {
                    displayAdvancedResults(data.results, filters);
                } else {
                    showError(data.error || 'An error occurred');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        function showLoading() {
            document.getElementById('resultsContainer').innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 class="mt-3">🔍 Processing your query...</h5>
                    <p class="text-muted">Searching hotels with AI intelligence</p>
                    <div class="progress mt-3" style="height: 6px; max-width: 300px; margin: 0 auto;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 100%"></div>
                    </div>
                    <div class="mt-3">
                        <small class="text-success d-block"><i class="fas fa-sync-alt fa-spin"></i> <strong>REAL-TIME DATA</strong> - No stale results</small>
                        <small class="text-info d-block"><i class="fas fa-shield-alt"></i> Cache disabled - Always fresh</small>
                        <small class="text-warning d-block"><i class="fas fa-clock"></i> Fetching live amenity data</small>
                    </div>
                </div>
            `;

            // Disable search button during processing
            const searchButton = document.querySelector('.btn-search');
            if (searchButton) {
                const originalText = searchButton.innerHTML;
                searchButton.disabled = true;
                searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';

                // Re-enable after a timeout (fallback)
                setTimeout(() => {
                    searchButton.disabled = false;
                    searchButton.innerHTML = originalText;
                }, 30000); // 30 second timeout
            }
        }

        function displayResults(result) {
            const container = document.getElementById('resultsContainer');
            let html = '';

            // Add real-time data indicator
            const currentTime = new Date().toLocaleTimeString();
            html += `<div class="alert alert-success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-sync-alt"></i> <strong>REAL-TIME DATA</strong> - No stale results
                    </div>
                    <small class="text-muted">Fetched: ${currentTime}</small>
                </div>
            </div>`;

            if (result.summary) {
                html += `<div class="alert alert-info">
                    <i class="fas fa-brain"></i> <strong>AI Analysis:</strong><br>
                    ${result.summary}
                </div>`;
            }

            if (result.results && result.results.length > 0) {
                updateStats(result.results);

                result.results.forEach(hotel => {
                    html += createDetailedHotelCard(hotel);
                });
            } else {
                html += `<div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No hotels found matching your criteria.
                </div>`;
            }

            container.innerHTML = html;
        }

        function displayAdvancedResults(results, filters) {
            const container = document.getElementById('resultsContainer');
            let html = '';

            if (results.length > 0) {
                updateStats(results);

                html += `<div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> Found ${results.length} hotels matching your criteria
                </div>`;

                results.forEach(hotel => {
                    html += createDetailedHotelCard(hotel, filters);
                });
            } else {
                html += `<div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No hotels found matching your criteria.
                </div>`;
            }

            container.innerHTML = html;
        }

        function createDetailedHotelCard(hotel, filters = {}) {
            const relevanceScore = hotel.relevance_score ? Math.round(hotel.relevance_score * 100) : 0;
            const matchQuality = hotel.match_quality || 'medium';
            const confidenceClass = matchQuality === 'high' ? 'confidence-high' : 'confidence-medium';

            let html = `
                <div class="hotel-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="hotel-name">
                            ${hotel.name}
                            ${matchQuality === 'high' ? '<span class="high-confidence-badge">HIGH CONFIDENCE</span>' : ''}
                        </div>
                        ${relevanceScore > 0 ? `<span class="relevance-score ${confidenceClass}">${relevanceScore}% Match</span>` : ''}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <i class="fas fa-map-marker-alt text-primary"></i>
                                ${hotel.city_name}${hotel.locality_name ? ', ' + hotel.locality_name : ''}
                            </p>
                            ${hotel.postal_address ? `<p class="mb-2 text-muted"><small>${hotel.postal_address}</small></p>` : ''}
                        </div>
                        <div class="col-md-6 text-end">
                            <a href="/hotel-details?id=${hotel.property_id}" class="btn btn-primary btn-sm" onclick="saveSearchState()">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                            <button class="btn btn-outline-primary btn-sm ms-1" onclick="getDetailedInfo(${hotel.property_id})">
                                <i class="fas fa-info-circle"></i> Quick Info
                            </button>
                            ${hotel.maps_link ? `<a href="${hotel.maps_link}" target="_blank" class="btn btn-outline-secondary btn-sm ms-1">
                                <i class="fas fa-map"></i> Map
                            </a>` : ''}
                        </div>
                    </div>
            `;

            // Add amenity information if available
            if (hotel.property_amenities || hotel.room_amenities) {
                html += '<div class="amenity-section">';

                if (hotel.property_amenities && Object.keys(hotel.property_amenities).length > 0) {
                    html += '<h6><i class="fas fa-building"></i> Property Amenities</h6>';
                    Object.keys(hotel.property_amenities).forEach(amenity => {
                        html += `<span class="amenity-badge property-amenity">${amenity.replace('_', ' ')}</span>`;
                    });
                }

                if (hotel.room_amenities && Object.keys(hotel.room_amenities).length > 0) {
                    html += '<h6 class="mt-3"><i class="fas fa-door-open"></i> Room Types & Amenities</h6>';
                    Object.keys(hotel.room_amenities).forEach(roomType => {
                        const roomClass = roomType.toLowerCase().includes('acacia') ? 'acacia' :
                                         roomType.toLowerCase().includes('oak') ? 'oak' :
                                         roomType.toLowerCase().includes('maple') ? 'maple' :
                                         roomType.toLowerCase().includes('mahogany') ? 'mahogany' : '';

                        html += `<div class="room-type-card ${roomClass}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <strong><i class="fas fa-bed"></i> ${roomType.charAt(0).toUpperCase() + roomType.slice(1)} Room</strong>
                                <small class="text-muted">${roomType.toUpperCase()}</small>
                            </div>`;

                        const roomAmenities = hotel.room_amenities[roomType];
                        if (roomAmenities && typeof roomAmenities === 'object') {
                            // Handle direct amenity object (like our sample data)
                            Object.entries(roomAmenities).forEach(([amenity, available]) => {
                                if (available) {
                                    const amenityName = amenity.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                                    html += `<span class="amenity-badge room-amenity">${amenityName}</span>`;
                                }
                            });
                        }
                        html += '</div>';
                    });
                }

                html += '</div>';
            }

            html += '</div>';
            return html;
        }

        async function getDetailedInfo(propertyId) {
            try {
                const response = await fetch(`/api/hotels/${propertyId}`);
                const data = await response.json();

                if (data.status === 'success') {
                    showDetailedModal(data.hotel);
                } else {
                    alert('Error fetching hotel details: ' + data.error);
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }

        function showDetailedModal(hotelData) {
            // Create and show a modal with detailed information
            const modalHtml = `
                <div class="modal fade" id="hotelDetailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${hotelData.basic_info?.name || 'Hotel Details'}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <pre>${JSON.stringify(hotelData, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('hotelDetailModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add new modal
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('hotelDetailModal'));
            modal.show();
        }

        function updateStats(results) {
            document.getElementById('statsRow').style.display = 'block';
            document.getElementById('totalResults').textContent = results.length;

            // Calculate average relevance
            const relevanceScores = results.filter(r => r.relevance_score).map(r => r.relevance_score);
            const avgRelevance = relevanceScores.length > 0 ?
                Math.round(relevanceScores.reduce((a, b) => a + b, 0) / relevanceScores.length * 100) : 0;
            document.getElementById('avgRelevance').textContent = avgRelevance + '%';

            // Count unique cities
            const uniqueCities = new Set(results.map(r => r.city_name));
            document.getElementById('citiesCount').textContent = uniqueCities.size;

            // Count room types
            const roomTypesCount = results.reduce((count, hotel) => {
                if (hotel.room_amenities) {
                    return count + Object.keys(hotel.room_amenities).length;
                }
                return count;
            }, 0);
            document.getElementById('roomTypesCount').textContent = roomTypesCount;
        }

        function clearFilters() {
            document.getElementById('naturalQuery').value = '';
            document.getElementById('hotelNameFilter').value = '';
            $('#cityFilter, #roomTypeFilter, #propertyAmenityFilter, #roomAmenityFilter').val(null).trigger('change');

            // Clear results
            document.getElementById('resultsContainer').innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Use the search panel to find hotels</h5>
                    <p class="text-muted">Try natural language queries or use advanced filters</p>
                </div>
            `;
            document.getElementById('statsRow').style.display = 'none';
        }

        function showError(message) {
            document.getElementById('resultsContainer').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
        }

        function setSearchQuery(query) {
            document.getElementById('naturalQuery').value = query;
            // Auto-execute the search
            processNaturalQuery();
        }

        function handleSearchKeyPress(event) {
            // Trigger search on Enter key press
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                processNaturalQuery();
            }
        }

        let searchTimeout;
        function handleSearchInput(event) {
            const query = event.target.value.trim();

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Hide suggestions if query is too short
            if (query.length < 3) {
                hideSuggestions();
                return;
            }

            // Debounce the suggestions
            searchTimeout = setTimeout(() => {
                showSearchSuggestions(query);
            }, 300);
        }

        function showSearchSuggestions(query) {
            const suggestions = generateSmartSuggestions(query);
            const suggestionsContainer = document.getElementById('searchSuggestions');

            if (suggestions.length === 0) {
                hideSuggestions();
                return;
            }

            let html = '';
            suggestions.forEach(suggestion => {
                html += `
                    <div class="suggestion-item" onclick="selectSuggestion('${suggestion.text}')">
                        <div class="suggestion-text">${suggestion.text}</div>
                        <div class="suggestion-description">${suggestion.description}</div>
                    </div>
                `;
            });

            suggestionsContainer.innerHTML = html;
            suggestionsContainer.style.display = 'block';
        }

        function hideSuggestions() {
            document.getElementById('searchSuggestions').style.display = 'none';
        }

        function selectSuggestion(suggestionText) {
            document.getElementById('naturalQuery').value = suggestionText;
            hideSuggestions();
            // Auto-trigger search when suggestion is selected
            processNaturalQuery();
        }

        function generateSmartSuggestions(query) {
            const queryLower = query.toLowerCase();
            const suggestions = [];

            // City-based suggestions
            const cities = ['Mumbai', 'Bangalore', 'Delhi', 'Chennai', 'Pune', 'Hyderabad', 'Kolkata', 'Goa', 'Shimla', 'Manali'];
            const amenities = ['swimming pool', 'spa', 'gym', 'restaurant', 'wifi', 'parking', 'banquet hall'];

            // Smart city suggestions
            cities.forEach(city => {
                if (city.toLowerCase().includes(queryLower) || queryLower.includes(city.toLowerCase())) {
                    suggestions.push({
                        text: `hotels in ${city}`,
                        description: `Find all hotels in ${city}`
                    });

                    // Add amenity combinations for the city
                    if (suggestions.length < 3) {
                        suggestions.push({
                            text: `hotels with wifi in ${city}`,
                            description: `Hotels with WiFi in ${city} (Brand Standard)`
                        });
                    }
                }
            });

            // Amenity-based suggestions
            amenities.forEach(amenity => {
                if (amenity.includes(queryLower) || queryLower.includes(amenity)) {
                    suggestions.push({
                        text: `hotels with ${amenity} in Mumbai`,
                        description: `Find hotels with ${amenity} facilities`
                    });
                }
            });

            // State-based suggestions
            if (queryLower.includes('himachal') || queryLower.includes('hp')) {
                suggestions.push({
                    text: 'hotels in Himachal Pradesh with mountain view',
                    description: 'Hotels in Shimla, Manali, Dharamshala'
                });
            }

            // Brand standard suggestions
            if (queryLower.includes('breakfast') || queryLower.includes('wifi') || queryLower.includes('geyser')) {
                suggestions.push({
                    text: `hotels with free ${queryLower.includes('breakfast') ? 'breakfast' : queryLower.includes('wifi') ? 'wifi' : 'geyser'} in Mumbai`,
                    description: 'Guaranteed Treebo brand standard amenity'
                });
            }

            return suggestions.slice(0, 5); // Limit to 5 suggestions
        }

        // Hide suggestions when clicking outside
        document.addEventListener('click', function(event) {
            const suggestionsContainer = document.getElementById('searchSuggestions');
            const searchInput = document.getElementById('naturalQuery');

            if (!suggestionsContainer.contains(event.target) && event.target !== searchInput) {
                hideSuggestions();
            }
        });

        // Search state management for back navigation
        function saveSearchState() {
            const searchState = {
                query: document.getElementById('naturalQuery').value,
                results: document.getElementById('resultsContainer').innerHTML,
                timestamp: Date.now(),
                hasResults: document.getElementById('resultsContainer').innerHTML.trim() !== ''
            };

            sessionStorage.setItem('hotelSearchState', JSON.stringify(searchState));
            console.log('Search state saved for back navigation');
        }

        function restoreSearchState() {
            const savedState = sessionStorage.getItem('hotelSearchState');

            if (savedState) {
                try {
                    const searchState = JSON.parse(savedState);

                    // Check if state is not too old (within 1 hour)
                    const oneHour = 60 * 60 * 1000;
                    if (Date.now() - searchState.timestamp < oneHour) {
                        // Restore query
                        if (searchState.query) {
                            document.getElementById('naturalQuery').value = searchState.query;
                        }

                        // Restore results if they exist
                        if (searchState.hasResults && searchState.results) {
                            document.getElementById('resultsContainer').innerHTML = searchState.results;
                            console.log('Search state restored successfully');

                            // Show a notification that state was restored
                            showNotification('🔄 Previous search results restored successfully!', 'success');

                            // Add a subtle highlight to indicate restored state
                            const resultsContainer = document.getElementById('resultsContainer');
                            resultsContainer.style.border = '2px solid #28a745';
                            resultsContainer.style.borderRadius = '8px';

                            // Remove highlight after 3 seconds
                            setTimeout(() => {
                                resultsContainer.style.border = '';
                                resultsContainer.style.borderRadius = '';
                            }, 3000);
                        }
                    } else {
                        // Clear old state
                        sessionStorage.removeItem('hotelSearchState');
                        console.log('Search state expired, cleared');
                    }
                } catch (e) {
                    console.error('Error restoring search state:', e);
                    sessionStorage.removeItem('hotelSearchState');
                }
            }
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        // Check for back navigation on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're returning from hotel details
            const urlParams = new URLSearchParams(window.location.search);
            const fromDetails = urlParams.get('from') === 'details';

            if (fromDetails) {
                // Remove the 'from' parameter from URL without page reload
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);

                // Restore search state
                restoreSearchState();
            }
        });
    </script>
</body>
</html>
