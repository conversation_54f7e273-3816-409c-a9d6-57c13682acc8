<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Portfolio Query Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFB74D 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin: 2rem auto;
            max-width: 1200px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .query-section {
            padding: 2rem;
        }

        .query-input {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .query-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-query {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-query:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .results-section {
            padding: 0 2rem 2rem;
        }

        .result-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .hotel-name {
            color: #667eea;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .hotel-location {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .amenity-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            margin: 0.2rem;
            display: inline-block;
        }

        .loading {
            text-align: center;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            margin: 1rem 0;
            border: 2px solid #667eea;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
        }

        .spinner {
            border: 6px solid #f3f3f3;
            border-top: 6px solid #667eea;
            border-right: 6px solid #764ba2;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1.2s linear infinite;
            margin: 0 auto 1rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 1.3rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .loading-details {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-top: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .loading-step {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0.5rem 0;
            font-size: 0.9rem;
        }

        .loading-step i {
            margin-right: 0.5rem;
            width: 20px;
        }

        .progress-bar-custom {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            animation: progress 2s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .example-queries {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
        }

        .example-query {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 0.8rem;
            margin: 0.5rem 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .example-query:hover {
            background: #667eea;
            color: white;
            transform: translateX(5px);
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <div class="row align-items-center mb-3">
                    <div class="col-md-3">
                        <div class="treebo-logo">
                            <span style="font-size: 2rem; font-weight: 700; color: white; font-family: 'Arial', sans-serif;">
                                Treebo
                            </span>
                        </div>
                    </div>
                    <div class="col-md-9 text-end">
                        <small class="text-light">
                            <i class="fas fa-shield-alt"></i> High Confidence Results Only
                        </small>
                    </div>
                </div>
                <h1><i class="fas fa-search"></i> Hotel Portfolio Query Tool</h1>
                <p class="mb-0">Search our hotel portfolio using natural language queries</p>
                <small class="text-light">✨ Find the perfect hotel for your needs</small>
            </div>

            <!-- Query Section -->
            <div class="query-section">
                <!-- City Dropdown -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="citySelect" class="form-label"><i class="fas fa-map-marker-alt"></i> Quick City Search</label>
                        <select id="citySelect" class="form-select query-input" onchange="handleCitySelection()">
                            <option value="">Select a city for quick search...</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label"><i class="fas fa-search"></i> Or use natural language</label>
                        <div class="text-muted small">Type your query below for advanced search</div>
                    </div>
                </div>

                <!-- Main Query Input -->
                <div class="row">
                    <div class="col-md-10">
                        <input type="text"
                               id="queryInput"
                               class="form-control query-input"
                               placeholder="Ask me anything about our hotels... e.g., 'Which hotel has banquet in Nagpur?'"
                               onkeypress="handleKeyPress(event)"
                               oninput="handleInputChange(event)"
                               autocomplete="off">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-primary btn-query w-100" onclick="processQuery()">
                            <i class="fas fa-search"></i> Search
                        </button>
                    </div>
                </div>

                <!-- Example Queries -->
                <div class="example-queries">
                    <h6><i class="fas fa-lightbulb"></i> Try these example queries:</h6>
                    <div class="example-query" onclick="setQuery('Find luxury hotels with swimming pool and spa in Mumbai')">
                        🏊‍♂️ Find luxury hotels with swimming pool and spa in Mumbai
                    </div>
                    <div class="example-query" onclick="setQuery('Which hotels in Bangalore have banquet facilities?')">
                        🎉 Which hotels in Bangalore have banquet facilities?
                    </div>
                    <div class="example-query" onclick="setQuery('Show me hotels with parking in Shimla')">
                        🚗 Show me hotels with parking in Shimla
                    </div>
                    <div class="example-query" onclick="setQuery('Hotels with mountain view in Himachal')">
                        🏔️ Hotels with mountain view in Himachal
                    </div>
                    <div class="example-query" onclick="setQuery('Hotels with gym and spa in Mumbai')">
                        💪 Hotels with gym and spa in Mumbai
                    </div>
                    <div class="example-query" onclick="setQuery('Business hotels with conference rooms in Pune')">
                        💼 Business hotels with conference rooms in Pune
                    </div>
                </div>
            </div>

            <!-- Results Section -->
            <div class="results-section" id="resultsSection" style="display: none;">
                <h4><i class="fas fa-list"></i> Results</h4>
                <div id="resultsContainer"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load cities on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCities();
        });

        async function loadCities() {
            try {
                const response = await fetch('/api/cities');
                const data = await response.json();

                if (data.status === 'success') {
                    const citySelect = document.getElementById('citySelect');

                    // Add popular cities first
                    const popularCities = ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Hyderabad', 'Pune', 'Kolkata', 'Ahmedabad', 'Jaipur', 'Kochi'];
                    const otherCities = data.cities.filter(city => !popularCities.includes(city)).sort();

                    // Add popular cities section
                    if (popularCities.some(city => data.cities.includes(city))) {
                        const popularGroup = document.createElement('optgroup');
                        popularGroup.label = 'Popular Cities';
                        popularCities.forEach(city => {
                            if (data.cities.includes(city)) {
                                const option = document.createElement('option');
                                option.value = city;
                                option.textContent = city;
                                popularGroup.appendChild(option);
                            }
                        });
                        citySelect.appendChild(popularGroup);
                    }

                    // Add other cities section
                    if (otherCities.length > 0) {
                        const otherGroup = document.createElement('optgroup');
                        otherGroup.label = 'All Cities';
                        otherCities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city;
                            option.textContent = city;
                            otherGroup.appendChild(option);
                        });
                        citySelect.appendChild(otherGroup);
                    }
                }
            } catch (error) {
                console.error('Error loading cities:', error);
            }
        }

        function handleCitySelection() {
            const citySelect = document.getElementById('citySelect');
            const selectedCity = citySelect.value;

            if (selectedCity) {
                // ENHANCED: Immediate auto-search for hotels in the selected city
                const query = `hotels in ${selectedCity}`;
                document.getElementById('queryInput').value = query;

                // Show immediate loading feedback
                showLoading();

                // Trigger search immediately
                console.log(`🔄 DROPDOWN SELECTION: Immediately searching for "${query}"`);
                processQuery();
            }
        }

        function setQuery(query) {
            document.getElementById('queryInput').value = query;
            // Clear city selection when using example queries
            document.getElementById('citySelect').value = '';

            // ENHANCED: Immediately trigger search when example query is clicked
            console.log(`🎯 EXAMPLE QUERY CLICKED: Immediately searching for "${query}"`);

            // Show immediate loading feedback
            showLoading();

            // Trigger search immediately
            processQuery();
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                // ENHANCED: Immediate Enter key response
                event.preventDefault(); // Prevent form submission

                const query = document.getElementById('queryInput').value.trim();
                if (query) {
                    console.log(`⚡ ENTER KEY PRESSED: Immediately searching for "${query}"`);

                    // Show immediate loading feedback
                    showLoading();

                    // Trigger search immediately
                    processQuery();
                } else {
                    // Show helpful message if query is empty
                    showError('Please enter a search query');
                }
            }
        }

        function handleInputChange(event) {
            // ENHANCED: Real-time input feedback
            const query = event.target.value.trim();

            // Clear any previous timeout
            if (window.inputTimeout) {
                clearTimeout(window.inputTimeout);
            }

            // Show search suggestions for longer queries
            if (query.length >= 3) {
                // Add subtle visual feedback that search is ready
                event.target.style.borderColor = '#28a745';
                event.target.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
            } else {
                // Reset styling
                event.target.style.borderColor = '';
                event.target.style.boxShadow = '';
            }
        }

        async function processQuery() {
            const query = document.getElementById('queryInput').value.trim();

            if (!query) {
                showError('Please enter a query');
                return;
            }

            showLoading();

            try {
                const response = await fetch('/api/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    displayResults(data.result);
                } else {
                    showError(data.error || 'An error occurred');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        function showLoading() {
            const resultsSection = document.getElementById('resultsSection');
            const resultsContainer = document.getElementById('resultsContainer');

            resultsContainer.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <div class="loading-text">🔍 Processing Your Query...</div>

                    <div class="progress-bar-custom">
                        <div class="progress-fill"></div>
                    </div>

                    <div class="loading-details">
                        <div class="loading-step">
                            <i class="fas fa-sync-alt fa-spin text-success"></i>
                            <strong>REAL-TIME SEARCH</strong> - Fetching fresh data
                        </div>
                        <div class="loading-step">
                            <i class="fas fa-brain fa-pulse text-info"></i>
                            <strong>AI INTELLIGENCE</strong> - Semantic analysis in progress
                        </div>
                        <div class="loading-step">
                            <i class="fas fa-shield-alt fa-beat text-warning"></i>
                            <strong>HIGH ACCURACY</strong> - Filtering best matches
                        </div>
                        <div class="loading-step">
                            <i class="fas fa-clock fa-spin text-primary"></i>
                            <strong>LIVE DATA</strong> - No stale results guaranteed
                        </div>
                    </div>

                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Please wait while we search through our hotel portfolio...
                        </small>
                    </div>
                </div>
            `;

            resultsSection.style.display = 'block';

            // Scroll to results section for better UX
            resultsSection.scrollIntoView({ behavior: 'smooth' });

            // Add visual feedback to search button
            const searchButton = document.querySelector('.btn-query');
            if (searchButton) {
                searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
                searchButton.disabled = true;
            }
        }

        function displayResults(result) {
            const resultsContainer = document.getElementById('resultsContainer');
            let html = '';

            // Reset search button
            const searchButton = document.querySelector('.btn-query');
            if (searchButton) {
                searchButton.innerHTML = '<i class="fas fa-search"></i> Search';
                searchButton.disabled = false;
            }

            // AI indicators hidden for cleaner interface

            // AI summary hidden for cleaner interface

            // Analysis details hidden for cleaner interface

            // Display results
            if (result.results && result.results.length > 0) {
                html += `<div class="mt-3">
                    <h5><i class="fas fa-search"></i> Found ${result.total_results} Results</h5>
                    <div class="row">`;

                result.results.forEach(hotel => {
                    html += createEnhancedHotelCard(hotel);
                });

                html += '</div></div>';
            } else if (result.total_results === 0) {
                html += '<div class="alert alert-warning">No hotels found matching your criteria.</div>';
            }

            // Display suggestions
            if (result.suggestions && result.suggestions.length > 0) {
                html += '<div class="mt-3"><h6><i class="fas fa-lightbulb"></i> Suggestions:</h6>';
                result.suggestions.forEach(suggestion => {
                    html += `<div class="alert alert-light">${suggestion}</div>`;
                });
                html += '</div>';
            }

            // Handle legacy result types for backward compatibility
            if (result.type) {
                if (result.type === 'specific_hotel_amenities') {
                    html += createSpecificHotelResult(result);
                } else if (result.type === 'hotel_details') {
                    html += createHotelDetailsResult(result);
                } else if (result.type === 'hotel_not_found') {
                    html += `<div class="error-message">${result.message}</div>`;
                } else if (result.type === 'general_help') {
                    html += `<div class="alert alert-info">${result.message.replace(/\n/g, '<br>')}</div>`;
                }
            }

            resultsContainer.innerHTML = html;
        }

        function createEnhancedHotelCard(hotel) {
            let amenityBadges = '';
            let relevanceIndicator = '';

            // Show matched amenities
            if (hotel.matched_amenities) {
                hotel.matched_amenities.forEach(amenity => {
                    amenityBadges += `<span class="amenity-badge">${amenity.replace('_', ' ')}</span>`;
                });
            }

            // Match scores hidden for cleaner interface

            // Enhanced amenity display from API data
            let apiAmenities = '';
            if (hotel.api_amenities && Object.keys(hotel.api_amenities).length > 0) {
                apiAmenities = '<div class="mt-2"><small class="text-muted">API Amenities Available</small></div>';
            }

            return `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="result-card">
                        <div class="hotel-name">
                            ${hotel.name}
                        </div>
                        <div class="hotel-location">
                            <i class="fas fa-map-marker-alt"></i>
                            ${hotel.city_name}${hotel.locality_name ? ', ' + hotel.locality_name : ''}
                        </div>
                        ${hotel.postal_address ? `<div class="mt-2"><small><i class="fas fa-address-card"></i> ${hotel.postal_address}</small></div>` : ''}
                        ${amenityBadges ? `<div class="mt-2">${amenityBadges}</div>` : ''}
                        ${apiAmenities}
                        <div class="mt-3">
                            ${hotel.maps_link ? `<a href="${hotel.maps_link}" target="_blank" class="btn btn-sm btn-outline-primary me-2"><i class="fas fa-map"></i> Map</a>` : ''}
                            <button class="btn btn-sm btn-primary" onclick="getHotelDetails(${hotel.property_id})">
                                <i class="fas fa-info-circle"></i> Details
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        // Keep the original function for backward compatibility
        function createHotelCard(hotel) {
            return createEnhancedHotelCard(hotel);
        }

        function createSpecificHotelResult(result) {
            let html = `<div class="result-card">`;
            html += `<div class="hotel-name">${result.hotel.name}</div>`;
            html += `<div class="hotel-location"><i class="fas fa-map-marker-alt"></i> ${result.hotel.city_name}</div>`;

            if (result.amenity_status) {
                html += '<div class="mt-3"><h6>Amenity Status:</h6>';
                for (const [amenity, status] of Object.entries(result.amenity_status)) {
                    const icon = status ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';
                    const statusText = status ? 'Available' : 'Not Available';
                    html += `<div><i class="${icon}"></i> ${amenity.replace('_', ' ')}: ${statusText}</div>`;
                }
                html += '</div>';
            }

            html += '</div>';
            return html;
        }

        function createHotelDetailsResult(result) {
            let html = `<div class="result-card">`;
            html += `<div class="hotel-name">${result.hotel.name}</div>`;
            html += `<div class="hotel-location"><i class="fas fa-map-marker-alt"></i> ${result.hotel.city_name}</div>`;

            if (result.details && result.details.basic_info) {
                const info = result.details.basic_info;
                if (info.postal_address) {
                    html += `<div class="mt-2"><strong>Address:</strong> ${info.postal_address}</div>`;
                }
                if (info.maps_link) {
                    html += `<div class="mt-2"><a href="${info.maps_link}" target="_blank" class="btn btn-sm btn-outline-primary"><i class="fas fa-map"></i> View on Map</a></div>`;
                }
            }

            html += '</div>';
            return html;
        }

        async function getHotelDetails(propertyId) {
            // Open the hotel details page in a new tab/window
            window.open(`/hotel-details?id=${propertyId}`, '_blank');
        }

        function showError(message) {
            const resultsSection = document.getElementById('resultsSection');
            const resultsContainer = document.getElementById('resultsContainer');

            resultsContainer.innerHTML = `<div class="error-message">${message}</div>`;
            resultsSection.style.display = 'block';

            // Reset search button
            const searchButton = document.querySelector('.btn-query');
            if (searchButton) {
                searchButton.innerHTML = '<i class="fas fa-search"></i> Search';
                searchButton.disabled = false;
            }
        }
    </script>
</body>
</html>
