<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hotel Details - Treebo Style</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --treebo-primary: #FF6B35;
            --treebo-secondary: #2C3E50;
            --treebo-accent: #F39C12;
            --treebo-light: #ECF0F1;
            --treebo-dark: #34495E;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 50%, #FFB74D 100%);
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        .hotel-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
        }

        .hotel-header {
            background: linear-gradient(135deg, var(--treebo-primary) 0%, var(--treebo-accent) 100%);
            color: white;
            padding: 2rem;
            position: relative;
        }

        .hotel-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .hotel-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .hotel-location {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .rating-badge {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 0.5rem 1rem;
            display: inline-block;
            margin-top: 1rem;
            position: relative;
            z-index: 1;
        }

        .content-section {
            padding: 2rem;
        }

        .amenities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .amenity-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            border-left: 5px solid var(--treebo-primary);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .amenity-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 107, 53, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .amenity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .amenity-card:hover::before {
            opacity: 1;
        }

        .amenity-title {
            color: var(--treebo-secondary);
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .amenity-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .amenity-tag {
            background: var(--treebo-primary);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }

        .amenity-tag:hover {
            background: var(--treebo-secondary);
            transform: scale(1.05);
        }

        .room-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .room-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .room-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .room-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        }

        .room-header {
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .room-header.acacia {
            background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
        }

        .room-header.oak {
            background: linear-gradient(135deg, #D2691E 0%, #CD853F 100%);
        }

        .room-header.maple {
            background: linear-gradient(135deg, #CD853F 0%, #DEB887 100%);
        }

        .room-header.mahogany {
            background: linear-gradient(135deg, #A0522D 0%, #8B4513 100%);
        }

        .room-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: rotate(45deg);
        }

        .room-name {
            color: white;
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }

        .room-type {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            position: relative;
            z-index: 1;
        }

        .room-content {
            padding: 1.5rem;
        }

        .room-amenities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .room-amenity-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }

        .room-amenity-item:hover {
            background: var(--treebo-light);
            transform: scale(1.02);
        }

        .room-amenity-icon {
            color: var(--treebo-primary);
            font-size: 1rem;
        }

        .section-title {
            color: var(--treebo-secondary);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            display: inline-block;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--treebo-primary);
            border-radius: 2px;
        }

        .back-button {
            background: var(--treebo-secondary);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .back-button:hover {
            background: var(--treebo-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .contact-section {
            background: var(--treebo-secondary);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .contact-button {
            background: var(--treebo-primary);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .contact-button:hover {
            background: var(--treebo-accent);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .amenity-icons {
            ac: '❄️',
            tv: '📺',
            balcony: '🏞️',
            room_service: '🛎️',
            mini_bar: '🍷',
            safe: '🔒',
            wifi: '📶',
            swimming_pool: '🏊‍♂️',
            spa: '💆‍♀️',
            banquet: '🎉',
            parking: '🚗',
            restaurant: '🍽️',
            gym: '💪',
            elevator: '🛗'
        }

        @media (max-width: 768px) {
            .hotel-title {
                font-size: 2rem;
            }

            .amenities-grid,
            .room-grid {
                grid-template-columns: 1fr;
            }

            .room-amenities {
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="hotel-container">
            <!-- Hotel Header -->
            <div class="hotel-header">
                <div class="row align-items-center mb-3">
                    <div class="col-md-2">
                        <div class="treebo-logo">
                            <span style="font-size: 1.6rem; font-weight: 700; color: white; font-family: 'Arial', sans-serif;">
                                Treebo
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="rating-badge">
                            <i class="fas fa-star"></i> Premium Property
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="#" class="back-button" onclick="smartBackNavigation(event)">
                            <i class="fas fa-arrow-left"></i> Back to Search
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <h1 class="hotel-title" id="hotelName">Loading...</h1>
                        <p class="hotel-location" id="hotelLocation">
                            <i class="fas fa-map-marker-alt"></i> Loading location...
                        </p>
                    </div>
                </div>
            </div>

            <!-- Property Amenities Section -->
            <div class="content-section">
                <h2 class="section-title">
                    <i class="fas fa-building"></i> Property Amenities
                </h2>
                <div class="amenities-grid" id="propertyAmenities">
                    <!-- Property amenities will be loaded here -->
                </div>
            </div>

            <!-- Room Types Section -->
            <div class="room-section">
                <h2 class="section-title">
                    <i class="fas fa-bed"></i> Room Types & Amenities
                </h2>
                <p class="text-muted">Each room type offers unique amenities and features designed for your comfort</p>
                <div class="room-grid" id="roomTypes">
                    <!-- Room types will be loaded here -->
                </div>
            </div>

            <!-- Contact Section -->
            <div class="contact-section">
                <h3><i class="fas fa-phone"></i> Ready to Book?</h3>
                <p>Contact us for the best rates and availability</p>
                <button class="contact-button">
                    <i class="fas fa-phone"></i> Call Now
                </button>
                <button class="contact-button">
                    <i class="fas fa-envelope"></i> Email Us
                </button>
                <button class="contact-button" id="viewOnMap">
                    <i class="fas fa-map"></i> View on Map
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Comprehensive amenity icons mapping
        const amenityIcons = {
            // Room amenities
            'ac_room': 'fas fa-snowflake',
            'flat_screen_tv': 'fas fa-tv',
            'balcony': 'fas fa-tree',
            'room_service': 'fas fa-bell',
            'mini_bar': 'fas fa-wine-glass',
            'safe': 'fas fa-lock',
            'geyser': 'fas fa-shower',
            'fan': 'fas fa-fan',
            'cupboards': 'fas fa-archive',
            'windows': 'fas fa-window-maximize',
            'intercom': 'fas fa-phone',
            'complimentary_toiletries': 'fas fa-soap',
            'study_table_chair': 'fas fa-chair',
            'luggage_shelf': 'fas fa-suitcase',

            // Property amenities
            'wifi': 'fas fa-wifi',
            'free_wifi': 'fas fa-wifi',
            'free_breakfast': 'fas fa-coffee',
            'breakfast': 'fas fa-coffee',
            'swimming_pool': 'fas fa-swimmer',
            'spa': 'fas fa-spa',
            'banquet': 'fas fa-glass-cheers',
            'parking': 'fas fa-car',
            'restaurant': 'fas fa-utensils',
            'gym': 'fas fa-dumbbell',
            'elevator': 'fas fa-elevator',
            'laundry': 'fas fa-tshirt',
            'guest_laundry': 'fas fa-tshirt',
            '24_hour_security': 'fas fa-shield-alt',
            'security': 'fas fa-shield-alt',
            'card_payment_accepted': 'fas fa-credit-card',
            'travel_desk': 'fas fa-map-marked-alt',
            'pantry': 'fas fa-utensils',
            'wheel_chair': 'fas fa-wheelchair',
            'smoke_alarm': 'fas fa-fire-extinguisher',
            'mosquito_repellent': 'fas fa-bug',
            'ironing_board': 'fas fa-tshirt',
            'lobby_furniture': 'fas fa-couch',
            'sofa_chair': 'fas fa-couch',
            'coffee_table': 'fas fa-table',
            'other_furniture': 'fas fa-chair',
            'lobby_ac': 'fas fa-snowflake',
            'lobby_smoke_alarm': 'fas fa-fire-extinguisher',
            'public_washroom': 'fas fa-restroom',
            'smoking_room': 'fas fa-smoking',

            // Bed types
            'queen_bed': 'fas fa-bed',
            'single_beds': 'fas fa-bed',
            'bed': 'fas fa-bed',

            // General
            'ac': 'fas fa-snowflake',
            'tv': 'fas fa-tv',
            'lock': 'fas fa-lock'
        };

        // Get hotel ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const hotelId = urlParams.get('id');

        // Load hotel details
        async function loadHotelDetails() {
            console.log('Loading hotel details for ID:', hotelId);

            if (!hotelId) {
                console.error('No hotel ID provided');
                document.getElementById('hotelName').textContent = 'Error: No Hotel ID Provided';
                document.getElementById('hotelLocation').innerHTML = '<i class="fas fa-exclamation-triangle"></i> Please go back and select a hotel';
                return;
            }

            try {
                console.log('Fetching hotel data from API...');
                const response = await fetch(`/api/hotels/${hotelId}`);
                const data = await response.json();
                console.log('API Response:', data);

                if (data.status === 'success') {
                    displayHotelDetails(data.hotel);
                } else {
                    console.error('Hotel not found:', data.error);
                    document.getElementById('hotelName').textContent = 'Hotel Not Found';
                    document.getElementById('hotelLocation').innerHTML = '<i class="fas fa-exclamation-triangle"></i> Hotel not found in database';
                }
            } catch (error) {
                console.error('Error loading hotel details:', error);
                document.getElementById('hotelName').textContent = 'Error Loading Hotel';
                document.getElementById('hotelLocation').innerHTML = '<i class="fas fa-exclamation-triangle"></i> Failed to load hotel details';
            }
        }

        function displayHotelDetails(hotel) {
            // Update header
            document.getElementById('hotelName').textContent = hotel.name;
            document.getElementById('hotelLocation').innerHTML = `
                <i class="fas fa-map-marker-alt"></i> ${hotel.city_name}, ${hotel.locality_name}
            `;

            // Display property amenities
            displayPropertyAmenities(hotel.property_amenities);

            // Display room types
            displayRoomTypes(hotel.room_amenities);

            // Set up map link
            if (hotel.maps_link) {
                document.getElementById('viewOnMap').onclick = () => {
                    window.open(hotel.maps_link, '_blank');
                };
            }
        }

        function displayPropertyAmenities(amenities) {
            const container = document.getElementById('propertyAmenities');

            if (!amenities || Object.keys(amenities).length === 0) {
                container.innerHTML = `
                    <div class="amenity-card">
                        <h4 class="amenity-title">
                            <i class="fas fa-info-circle"></i> Treebo Brand Standards
                        </h4>
                        <div class="amenity-list">
                            <span class="amenity-tag">
                                <i class="fas fa-wifi"></i> Free WiFi
                            </span>
                            <span class="amenity-tag">
                                <i class="fas fa-coffee"></i> Free Breakfast
                            </span>
                            <span class="amenity-tag">
                                <i class="fas fa-shower"></i> Hot Water
                            </span>
                            <span class="amenity-tag">
                                <i class="fas fa-shield-alt"></i> 24/7 Security
                            </span>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            <i class="fas fa-star"></i> These amenities are guaranteed across all Treebo properties
                        </small>
                    </div>
                `;
                return;
            }

            // Group amenities by category for better organization
            const amenityGroups = {
                'Essential Services': ['free_breakfast', 'wifi', 'geyser', '24_hour_security', 'card_payment_accepted'],
                'Hotel Facilities': ['restaurant', 'parking', 'elevator', 'laundry', 'travel_desk'],
                'Recreation & Wellness': ['swimming_pool', 'spa', 'gym', 'banquet'],
                'Safety & Security': ['smoke_alarm', 'mosquito_repellent', 'wheel_chair']
            };

            let html = '';
            let hasAnyAmenities = false;

            Object.entries(amenityGroups).forEach(([category, categoryAmenities]) => {
                const availableAmenities = categoryAmenities.filter(amenity => amenities[amenity]);

                if (availableAmenities.length > 0) {
                    hasAnyAmenities = true;
                    html += `
                        <div class="amenity-card">
                            <h4 class="amenity-title">
                                <i class="fas fa-star"></i> ${category}
                            </h4>
                            <div class="amenity-list">
                    `;

                    availableAmenities.forEach(amenity => {
                        const icon = amenityIcons[amenity] || 'fas fa-check';
                        const name = amenity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                        html += `
                            <span class="amenity-tag">
                                <i class="${icon}"></i> ${name}
                            </span>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                }
            });

            // Add any other amenities that don't fit in categories
            const categorizedAmenities = Object.values(amenityGroups).flat();
            const otherAmenities = Object.keys(amenities).filter(amenity =>
                amenities[amenity] && !categorizedAmenities.includes(amenity)
            );

            if (otherAmenities.length > 0) {
                hasAnyAmenities = true;
                html += `
                    <div class="amenity-card">
                        <h4 class="amenity-title">
                            <i class="fas fa-plus"></i> Additional Amenities
                        </h4>
                        <div class="amenity-list">
                `;

                otherAmenities.forEach(amenity => {
                    const icon = amenityIcons[amenity] || 'fas fa-check';
                    const name = amenity.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                    html += `
                        <span class="amenity-tag">
                            <i class="${icon}"></i> ${name}
                        </span>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            container.innerHTML = html || `
                <div class="amenity-card">
                    <h4 class="amenity-title">
                        <i class="fas fa-info-circle"></i> Amenity Information
                    </h4>
                    <p class="text-muted">Detailed amenity information is being updated. Please contact the hotel directly for the latest amenities.</p>
                </div>
            `;
        }

        function displayRoomTypes(roomAmenities) {
            const container = document.getElementById('roomTypes');

            if (!roomAmenities || Object.keys(roomAmenities).length === 0) {
                container.innerHTML = '<p class="text-muted">No room information available.</p>';
                return;
            }

            let html = '';

            Object.entries(roomAmenities).forEach(([roomType, amenities]) => {
                const roomClass = roomType.toLowerCase();
                const roomName = roomType.charAt(0).toUpperCase() + roomType.slice(1);

                html += `
                    <div class="room-card">
                        <div class="room-header ${roomClass}">
                            <h3 class="room-name">${roomName} Room</h3>
                            <p class="room-type">Premium ${roomName} Collection</p>
                        </div>
                        <div class="room-content">
                            <h5>Room Amenities</h5>
                            <div class="room-amenities">
                `;

                if (typeof amenities === 'object') {
                    Object.entries(amenities).forEach(([amenity, available]) => {
                        if (available) {
                            const icon = amenityIcons[amenity] || 'fas fa-check';
                            const name = amenity.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

                            html += `
                                <div class="room-amenity-item">
                                    <i class="${icon} room-amenity-icon"></i>
                                    <span>${name}</span>
                                </div>
                            `;
                        }
                    });
                }

                html += `
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    All ${roomName} rooms feature modern amenities and elegant design
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Smart back navigation that preserves search state
        function smartBackNavigation(event) {
            event.preventDefault();

            // Check if there's a saved search state
            const savedState = sessionStorage.getItem('hotelSearchState');

            if (savedState) {
                try {
                    const searchState = JSON.parse(savedState);

                    // Check if state is not too old (within 1 hour)
                    const oneHour = 60 * 60 * 1000;
                    if (Date.now() - searchState.timestamp < oneHour && searchState.hasResults) {
                        // Navigate back with 'from' parameter to trigger state restoration
                        window.location.href = '/dashboard?from=details';
                        return;
                    }
                } catch (e) {
                    console.error('Error checking search state:', e);
                }
            }

            // Fallback to regular dashboard navigation
            window.location.href = '/dashboard';
        }

        // Load hotel details when page loads
        document.addEventListener('DOMContentLoaded', loadHotelDetails);
    </script>
</body>
</html>
