#!/usr/bin/env python3
"""
Public server runner using ngrok for sharing the hotel search tool globally
"""

import os
import subprocess
import sys
import time
import threading
import requests
import json
from config import Config

def check_ngrok_installed():
    """Check if ngrok is installed"""
    try:
        subprocess.run(['ngrok', 'version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def install_ngrok_instructions():
    """Print instructions for installing ngrok"""
    print("❌ ngrok is not installed!")
    print("\n📥 To install ngrok:")
    print("1. Visit: https://ngrok.com/download")
    print("2. Download and install ngrok for your OS")
    print("3. Sign up for a free account at https://ngrok.com/")
    print("4. Get your auth token from https://dashboard.ngrok.com/get-started/your-authtoken")
    print("5. Run: ngrok config add-authtoken YOUR_TOKEN")
    print("\n🍺 Or install via Homebrew (macOS):")
    print("   brew install ngrok/ngrok/ngrok")

def get_ngrok_url():
    """Get the public URL from ngrok API"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels')
        tunnels = response.json()['tunnels']
        for tunnel in tunnels:
            if tunnel['proto'] == 'https':
                return tunnel['public_url']
        return None
    except Exception:
        return None

def start_flask_app(app_choice):
    """Start the Flask application"""
    if app_choice == "1":
        app_file = "simple_app.py"
        print("🚀 Starting Simple App...")
    else:
        app_file = "app.py"
        print("🚀 Starting Full App...")
    
    env = os.environ.copy()
    env['FLASK_ENV'] = 'development'
    env['DEBUG'] = 'False'  # Disable debug for public access
    env['PORT'] = '5000'
    
    subprocess.run([sys.executable, app_file], env=env)

def main():
    """Run the server with public access via ngrok"""
    
    print("=" * 70)
    print("🏨 Hotel Portfolio Query Tool - Public Server (via ngrok)")
    print("=" * 70)
    
    # Check if ngrok is installed
    if not check_ngrok_installed():
        install_ngrok_instructions()
        return
    
    # Choose which app to run
    app_choice = input("\nWhich app would you like to run?\n1. Simple App (demo with sample data)\n2. Full App (with database)\nEnter choice (1 or 2): ").strip()
    
    port = 5000
    
    print(f"\n🌐 Starting ngrok tunnel on port {port}...")
    
    # Start ngrok in background
    ngrok_process = subprocess.Popen([
        'ngrok', 'http', str(port), '--log=stdout'
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # Wait a moment for ngrok to start
    time.sleep(3)
    
    # Get the public URL
    public_url = get_ngrok_url()
    
    if public_url:
        print("=" * 70)
        print("✅ Public tunnel created successfully!")
        print("=" * 70)
        print(f"🌍 Public URL: {public_url}")
        print(f"🏠 Local URL: http://localhost:{port}")
        print("=" * 70)
        print("📱 Share this URL with your colleague:")
        print(f"   {public_url}")
        print("=" * 70)
        print("🔧 Available Endpoints:")
        print(f"   • Main Interface: {public_url}/")
        print(f"   • Dashboard: {public_url}/dashboard")
        print(f"   • Hotel Details: {public_url}/hotel-details")
        print(f"   • API Health: {public_url}/health")
        print("=" * 70)
        print("⚠️  SECURITY NOTE: This creates a public URL accessible to anyone!")
        print("🛑 Press Ctrl+C to stop both servers")
        print("=" * 70)
        
        # Start Flask app in a separate thread
        flask_thread = threading.Thread(target=start_flask_app, args=(app_choice,))
        flask_thread.daemon = True
        flask_thread.start()
        
        try:
            # Keep the main thread alive
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n🛑 Stopping servers...")
            ngrok_process.terminate()
            print("✅ Servers stopped")
    else:
        print("❌ Failed to get ngrok URL. Make sure ngrok is properly configured.")
        ngrok_process.terminate()

if __name__ == "__main__":
    main()
