#!/usr/bin/env python3
"""
Database setup script for Hotel Portfolio Query Tool
This script creates the necessary database tables and imports CSV data
"""

import pandas as pd
import psycopg2
from psycopg2.extras import execute_values
import logging
from config import Config
import sys
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_connection():
    """Create database connection"""
    try:
        connection = psycopg2.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            database=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )
        return connection
    except Exception as e:
        logger.error(f"Failed to connect to database: {str(e)}")
        return None

def create_tables(connection):
    """Create necessary database tables"""
    try:
        cursor = connection.cursor()
        
        # Create hotel_metadata table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS hotel_metadata (
            property_id INTEGER PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL,
            city_name VARCHAR(100),
            locality_name VARCHAR(100),
            micro_market_name VARCHAR(100),
            state_name VARCHAR(100),
            latitude DECIMAL(10, 8),
            longitude DECIMAL(11, 8),
            postal_address TEXT,
            maps_link TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        cursor.execute(create_table_sql)
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_hotel_city ON hotel_metadata(city_name);",
            "CREATE INDEX IF NOT EXISTS idx_hotel_status ON hotel_metadata(status);",
            "CREATE INDEX IF NOT EXISTS idx_hotel_name ON hotel_metadata(name);",
            "CREATE INDEX IF NOT EXISTS idx_hotel_location ON hotel_metadata(latitude, longitude);",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        # Enable pg_trgm extension for similarity search (if available)
        try:
            cursor.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm;")
            logger.info("pg_trgm extension enabled for fuzzy search")
        except Exception as e:
            logger.warning(f"Could not enable pg_trgm extension: {str(e)}")
        
        connection.commit()
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Error creating tables: {str(e)}")
        connection.rollback()
        raise

def import_csv_data(connection, csv_file_path):
    """Import data from CSV file"""
    try:
        if not os.path.exists(csv_file_path):
            logger.error(f"CSV file not found: {csv_file_path}")
            return False
        
        # Read CSV file
        df = pd.read_csv(csv_file_path)
        logger.info(f"Read {len(df)} rows from CSV file")
        
        # Clean data
        df = df.fillna('')  # Replace NaN with empty strings
        
        # Add state_name column if not present (you can enhance this)
        if 'state_name' not in df.columns:
            df['state_name'] = df['city_name'].apply(get_state_from_city)
        
        cursor = connection.cursor()
        
        # Clear existing data
        cursor.execute("DELETE FROM hotel_metadata;")
        logger.info("Cleared existing data")
        
        # Prepare data for insertion
        columns = [
            'property_id', 'name', 'status', 'city_name', 'locality_name',
            'micro_market_name', 'state_name', 'latitude', 'longitude',
            'postal_address', 'maps_link'
        ]
        
        # Ensure all required columns exist
        for col in columns:
            if col not in df.columns:
                df[col] = ''
        
        # Convert data to list of tuples
        data_tuples = []
        for _, row in df.iterrows():
            tuple_data = []
            for col in columns:
                value = row[col]
                # Handle numeric columns
                if col in ['property_id', 'latitude', 'longitude']:
                    if pd.isna(value) or value == '':
                        value = None
                    elif col == 'property_id':
                        value = int(float(value)) if value is not None else None
                    else:
                        value = float(value) if value is not None else None
                tuple_data.append(value)
            data_tuples.append(tuple(tuple_data))
        
        # Insert data
        insert_sql = f"""
        INSERT INTO hotel_metadata ({', '.join(columns)})
        VALUES %s
        ON CONFLICT (property_id) DO UPDATE SET
            name = EXCLUDED.name,
            status = EXCLUDED.status,
            city_name = EXCLUDED.city_name,
            locality_name = EXCLUDED.locality_name,
            micro_market_name = EXCLUDED.micro_market_name,
            state_name = EXCLUDED.state_name,
            latitude = EXCLUDED.latitude,
            longitude = EXCLUDED.longitude,
            postal_address = EXCLUDED.postal_address,
            maps_link = EXCLUDED.maps_link,
            updated_at = CURRENT_TIMESTAMP;
        """
        
        execute_values(cursor, insert_sql, data_tuples)
        connection.commit()
        
        # Verify import
        cursor.execute("SELECT COUNT(*) FROM hotel_metadata;")
        count = cursor.fetchone()[0]
        logger.info(f"Successfully imported {count} hotels to database")
        
        return True
        
    except Exception as e:
        logger.error(f"Error importing CSV data: {str(e)}")
        connection.rollback()
        return False

def get_state_from_city(city_name):
    """
    Map city names to states (basic implementation)
    You can enhance this with a proper city-to-state mapping
    """
    city_state_mapping = {
        'mumbai': 'Maharashtra',
        'pune': 'Maharashtra',
        'nagpur': 'Maharashtra',
        'bangalore': 'Karnataka',
        'delhi': 'Delhi',
        'hyderabad': 'Telangana',
        'chennai': 'Tamil Nadu',
        'kolkata': 'West Bengal',
        'ahmedabad': 'Gujarat',
        'jaipur': 'Rajasthan',
        'lucknow': 'Uttar Pradesh',
        'kanpur': 'Uttar Pradesh',
        'indore': 'Madhya Pradesh',
        'bhopal': 'Madhya Pradesh',
        'patna': 'Bihar',
        'vadodara': 'Gujarat',
        'ghaziabad': 'Uttar Pradesh',
        'ludhiana': 'Punjab',
        'agra': 'Uttar Pradesh',
        'nashik': 'Maharashtra',
        'pondicherry': 'Puducherry',
        'goa': 'Goa',
        'kochi': 'Kerala',
        'shimla': 'Himachal Pradesh',
        'udaipur': 'Rajasthan'
    }
    
    city_lower = city_name.lower() if city_name else ''
    return city_state_mapping.get(city_lower, 'Unknown')

def verify_setup(connection):
    """Verify database setup"""
    try:
        cursor = connection.cursor()
        
        # Check table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'hotel_metadata'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        if not table_exists:
            logger.error("hotel_metadata table does not exist")
            return False
        
        # Check data count
        cursor.execute("SELECT COUNT(*) FROM hotel_metadata WHERE status = 'LIVE';")
        live_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(DISTINCT city_name) FROM hotel_metadata WHERE status = 'LIVE';")
        city_count = cursor.fetchone()[0]
        
        logger.info(f"Database verification successful:")
        logger.info(f"  - {live_count} LIVE hotels")
        logger.info(f"  - {city_count} unique cities")
        
        # Show sample data
        cursor.execute("""
            SELECT name, city_name, state_name 
            FROM hotel_metadata 
            WHERE status = 'LIVE' 
            LIMIT 5;
        """)
        
        sample_data = cursor.fetchall()
        logger.info("Sample hotels:")
        for hotel in sample_data:
            logger.info(f"  - {hotel[0]} in {hotel[1]}, {hotel[2]}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error verifying setup: {str(e)}")
        return False

def main():
    """Main setup function"""
    logger.info("Starting database setup for Hotel Portfolio Query Tool")
    
    # Check if CSV file exists
    csv_file = Config.HOTEL_METADATA_FILE
    if not os.path.exists(csv_file):
        logger.error(f"CSV file not found: {csv_file}")
        logger.info("Please ensure hotel_metadata.csv exists in the project root")
        sys.exit(1)
    
    # Create database connection
    connection = create_connection()
    if not connection:
        logger.error("Could not connect to database")
        sys.exit(1)
    
    try:
        # Create tables
        create_tables(connection)
        
        # Import CSV data
        if import_csv_data(connection, csv_file):
            logger.info("CSV data imported successfully")
        else:
            logger.error("Failed to import CSV data")
            sys.exit(1)
        
        # Verify setup
        if verify_setup(connection):
            logger.info("Database setup completed successfully!")
            logger.info("\nNext steps:")
            logger.info("1. Set your OpenAI API key in .env file")
            logger.info("2. Run: python app.py")
            logger.info("3. Open http://localhost:5000 in your browser")
        else:
            logger.error("Database verification failed")
            sys.exit(1)
    
    finally:
        connection.close()

if __name__ == "__main__":
    main()
