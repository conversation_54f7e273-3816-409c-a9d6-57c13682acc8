from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
import base64

# Replace with your actual keys as byte strings (-----BEGIN ...-----)
PRIVATE_KEY_PEM = b"""***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""

PUBLIC_KEY_PEM = b"""-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsa4OwxrUmtIX5K25KGmB
/T0zBfo4+hFqtfB/x5/3ReESjjsmOn8AOk9uyerEHUbf8MkRzqw66BwD8vq33DcQ
ubWDXxspo2yK+mhh3gNTXlM4yJ8pY7Z+nm2pzo+5Nc9rj1ZTHNLz8zUK2L8sUeSU
6I9lF8/b/06h768bX5toR6uHtY7C57TV2nVsUYJ5EEDmKUQY/0/uFvJPnFNgVyZD
Gp7kvUCy55vZexncaz/LbZ1JWFIwTmQLdpBWLUPIsoIW5n41759EfdAPYFmEG1FJ
PSLby+vkw+4EYpG9dCmnKvSoFpwRdjeZffGJlAD/TA2gmU/bdWJLqo4rnp9cJ3Oq
nQIDAQAB
-----END PUBLIC KEY-----"""

# Message to sign (like a login challenge or payload)
challenge = "verify-this-signature"

# Load the private key from PEM
private_key = serialization.load_pem_private_key(
    PRIVATE_KEY_PEM,
    password=None,
)

# Sign the message using private key
signature = private_key.sign(
    challenge.encode(),
    padding.PKCS1v15(),
    hashes.SHA256()
)
signature_b64 = base64.b64encode(signature).decode()
print("🔏 Signature (Base64):", signature_b64)

# Load the public key from PEM
public_key = serialization.load_pem_public_key(PUBLIC_KEY_PEM)

# Verify the signature
try:
    public_key.verify(
        base64.b64decode(signature_b64),
        challenge.encode(),
        padding.PKCS1v15(),
        hashes.SHA256()
    )
    print("✅ Signature verification passed.")
except Exception as e:
    print("❌ Signature verification failed:", e)


pem_string = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsa4OwxrUmtIX5K25KGmB
/T0zBfo4+hFqtfB/x5/3ReESjjsmOn8AOk9uyerEHUbf8MkRzqw66BwD8vq33DcQ
ubWDXxspo2yK+mhh3gNTXlM4yJ8pY7Z+nm2pzo+5Nc9rj1ZTHNLz8zUK2L8sUeSU
6I9lF8/b/06h768bX5toR6uHtY7C57TV2nVsUYJ5EEDmKUQY/0/uFvJPnFNgVyZD
Gp7kvUCy55vZexncaz/LbZ1JWFIwTmQLdpBWLUPIsoIW5n41759EfdAPYFmEG1FJ
PSLby+vkw+4EYpG9dCmnKvSoFpwRdjeZffGJlAD/TA2gmU/bdWJLqo4rnp9cJ3Oq
nQIDAQAB
-----END PUBLIC KEY-----"""

with open("public_key.pem", "w") as f:
    f.write(pem_string)
